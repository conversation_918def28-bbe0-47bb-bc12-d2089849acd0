#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 获取文档文件相对于assets目录的深度
function getRelativeDepth(filePath) {
  // 计算从 src/content/docs/ 到文件的深度
  const relativePath = filePath.replace('src/content/docs/', '');
  const depth = relativePath.split('/').length - 1; // 减1因为最后一个是文件名
  return '../'.repeat(depth + 2); // +2 是因为要从docs回到src，再到assets
}

// 修复单个文件中的图片路径
function fixImagePaths(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePrefix = getRelativeDepth(filePath);
    
    // 替换所有 /src/assets/ 为正确的相对路径
    const fixedContent = content.replace(
      /!\[([^\]]*)\]\(\/src\/assets\/([^)]+)\)/g,
      `![$1](${relativePrefix}assets/$2)`
    );
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const filesToFix = [
    'src/content/docs/order/statement/goods-summary.md',
    'src/content/docs/order/statement/customer-summary.md',
    'src/content/docs/order/statement/staff-summary.md',
    'src/content/docs/order/manage/order-query.md',
    'src/content/docs/order/manage/returns.md',
    'src/content/docs/distribution/cashout.md',
    'src/content/docs/distribution/distributor.md',
    'src/content/docs/system/notice-settings/index.md',
    'src/content/docs/system/wx-mp/mp-publish.md',
    'src/content/docs/system/wx-mp/mp-settings.md',
    'src/content/docs/system/organizational/employee.md',
    'src/content/docs/system/organizational/department.md',
    'src/content/docs/system/organizational/role.md',
    'src/content/docs/system/other/route-settings.md',
    'src/content/docs/system/other/driver-list.md',
    'src/content/docs/system/other/process-settings.md',
    'src/content/docs/system/other/delivery-settings.md',
    'src/content/docs/system/other/pay-settings.md',
    'src/content/docs/system/shop-decoration/style-settings.md',
    'src/content/docs/system/shop-decoration/category-template.md',
    'src/content/docs/system/shop-decoration/start-page.md',
    'src/content/docs/system/shop-decoration/page-design.md',
    'src/content/docs/system/base/voice-settings.md',
    'src/content/docs/system/base/push-notification.md',
    'src/content/docs/purchase/statement/supplier-summary.md',
    'src/content/docs/purchase/statement/goods-summary.md',
    'src/content/docs/purchase/statement/staff-summary.md',
    'src/content/docs/purchase/statement/purchase-detail.md',
    'src/content/docs/purchase/manage/stockout.md',
    'src/content/docs/purchase/manage/purchase-order.md',
    'src/content/docs/purchase/manage/supplier.md',
    'src/content/docs/purchase/manage/cost-price.md',
    'src/content/docs/purchase/manage/purchase-return.md',
    'src/content/docs/purchase/manage/merchant.md',
    'src/content/docs/marketing/promotion/promotion-list.md',
    'src/content/docs/marketing/cashier/cashier-list.md',
    'src/content/docs/marketing/vip/collection-records.md',
    'src/content/docs/marketing/vip/membership-card.md',
    'src/content/docs/marketing/commission/commission-statistic.md',
    'src/content/docs/marketing/commission/commission-rule.md',
    'src/content/docs/marketing/set-meal/index.md',
    'src/content/docs/marketing/money-goods-bill/finance-daily-reconciliation.md',
    'src/content/docs/marketing/money-goods-bill/sales-daily-reconciliation.md',
    'src/content/docs/marketing/distribution/settings.md',
    'src/content/docs/marketing/distribution/overview.md',
    'src/content/docs/marketing/distribution/businessman.md',
    'src/content/docs/marketing/distribution/distribution-list.md',
    'src/content/docs/marketing/distribution/levels.md',
    'src/content/docs/marketing/distribution/cashout.md',
    'src/content/docs/marketing/distribution/goods-list.md',
    'src/content/docs/marketing/distribution/order-list.md',
    'src/content/docs/marketing/coupon/coupon-list.md',
    'src/content/docs/marketing/full-buy/full-buy-list.md',
    'src/content/docs/marketing/car-sale/vehicle-list.md',
    'src/content/docs/marketing/car-sale/level-management.md',
    'src/content/docs/marketing/car-sale/employee-settlement.md',
    'src/content/docs/marketing/car-sale/sales-record.md',
    'src/content/docs/marketing/supplier/supplier-set.md',
    'src/content/docs/marketing/multistore/store-list.md',
    'src/content/docs/marketing/bill-template/bill-template.md',
    'src/content/docs/marketing/points-mall/goods-manage.md',
    'src/content/docs/marketing/points-mall/point-rule.md',
    'src/content/docs/marketing/points-mall/exchange-record.md',
    'src/content/docs/marketing/full-give/full-give-list.md',
    'src/content/docs/finance/section1/apply-receipt.md',
    'src/content/docs/finance/section1/receivables.md',
    'src/content/docs/finance/section1/customer-balance-detail.md',
    'src/content/docs/finance/section1/receipts.md',
    'src/content/docs/finance/section1/sales-refund.md',
    'src/content/docs/finance/section3/fund-transfer.md',
    'src/content/docs/finance/section3/account-detail-query.md',
    'src/content/docs/finance/section4/expense-order.md',
    'src/content/docs/finance/section4/balance-withdrawal.md',
    'src/content/docs/finance/section4/finance-types.md',
    'src/content/docs/finance/section4/expense-types.md',
    'src/content/docs/finance/section2/supplier-summary.md',
    'src/content/docs/finance/section2/supplier-balance.md',
    'src/content/docs/finance/section2/payments.md',
    'src/content/docs/finance/section2/supplier-balance-detail.md',
    'src/content/docs/goods/price/adjust-price-goods.md',
    'src/content/docs/goods/price/price-table.md',
    'src/content/docs/goods/manage/unit-measurement.md',
    'src/content/docs/goods/manage/goods-classify.md',
    'src/content/docs/goods/manage/goods-grouping.md',
    'src/content/docs/stock/section3/warehouse-area.md',
    'src/content/docs/stock/section3/warehouse-location.md',
    'src/content/docs/stock/section3/stocktake.md',
    'src/content/docs/stock/section3/warehouse.md',
    'src/content/docs/stock/section3/loss-report.md',
    'src/content/docs/stock/section3/allocate.md',
    'src/content/docs/stock/section2/inventory-summary.md',
    'src/content/docs/stock/section2/warehouse-inventory.md',
    'src/content/docs/stock/section2/inventory.md',
    'src/content/docs/stock/section2/flow.md',
    'src/content/docs/stock/section2/batch.md',
    'src/content/docs/stock/supplier/supplier-inventory-flowing.md',
    'src/content/docs/stock/supplier/supplier-inventory.md',
    'src/content/docs/supplier-portal/account-management.md',
    'src/content/docs/supplier-portal/settlement-details.md',
    'src/content/docs/supplier-portal/overview.md',
    'src/content/docs/supplier-portal/goods-management.md',
    'src/content/docs/supplier-portal/order-management.md',
    'src/content/docs/supplier-portal/inventory-status.md',
    'src/content/docs/supplier-portal/fixed-price.md',
    'src/content/docs/supplier-portal/settlement-records.md',
    'src/content/docs/supplier-portal/goods-flow.md',
    'src/content/docs/overview/long-image.md',
    'src/content/docs/customer/behavior/purchase-history.md',
    'src/content/docs/customer/behavior/demand-report.md',
    'src/content/docs/customer/behavior/browsing-history.md',
    'src/content/docs/customer/statement/abnormal-analysis.md',
    'src/content/docs/customer/statement/new-customer-stats.md',
    'src/content/docs/customer/statement/visit-report.md',
    'src/content/docs/customer/statement/customer-map.md',
    'src/content/docs/customer/manage/customer-list.md',
    'src/content/docs/customer/manage/customer-type.md',
    'src/content/docs/customer/manage/label-management.md',
    'src/content/docs/customer/check/incomplete-data.md',
    'src/content/docs/customer/check/unaudited.md',
    'src/content/docs/reports/customer-goods-report.md',
    'src/content/docs/reports/customer-order-report.md',
    'src/content/docs/reports/salesman-order-report.md',
    'src/content/docs/reports/goods-sales-report.md',
    'src/content/docs/reports/regional-order-report.md',
    'src/content/docs/reports/order-data-report.md'
  ];

  console.log('🔧 Starting image path fixes...\n');
  
  let fixedCount = 0;
  let totalCount = 0;
  
  filesToFix.forEach(filePath => {
    totalCount++;
    if (fixImagePaths(filePath)) {
      fixedCount++;
    }
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`   Total files processed: ${totalCount}`);
  console.log(`   Files fixed: ${fixedCount}`);
  console.log(`   Files unchanged: ${totalCount - fixedCount}`);
}

main();
