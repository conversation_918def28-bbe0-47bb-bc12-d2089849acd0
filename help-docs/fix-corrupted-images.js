#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 检查文件是否为真正的图片文件
function isValidImage(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    // 如果文件包含HTML标签或纯文本，则认为是损坏的
    if (content.includes('<html>') || content.includes('<center>') || 
        content.includes('保存') || content.includes('截图') ||
        content.length < 100) {
      return false;
    }
    return true;
  } catch (error) {
    // 如果无法以UTF8读取，可能是真正的二进制图片文件
    return true;
  }
}

// 查找所有损坏的图片文件
function findCorruptedImages() {
  const corruptedFiles = [];
  
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanDirectory(filePath);
      } else if (file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg')) {
        if (!isValidImage(filePath)) {
          corruptedFiles.push(filePath);
        }
      }
    }
  }
  
  scanDirectory('src/assets');
  return corruptedFiles;
}

// 在markdown文件中注释掉对损坏图片的引用
function commentOutImageReferences(corruptedFiles) {
  const imageNames = corruptedFiles.map(file => path.basename(file));
  let fixedCount = 0;
  
  function processMarkdownFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      for (const imageName of imageNames) {
        const regex = new RegExp(`!\\[([^\\]]*)\\]\\(([^)]*${imageName.replace('.', '\\.')})\\)`, 'g');
        const newContent = content.replace(regex, '<!-- ![$1]($2) -->');
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Fixed references in: ${filePath}`);
        fixedCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }
  
  function scanMarkdownFiles(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        scanMarkdownFiles(filePath);
      } else if (file.endsWith('.md')) {
        processMarkdownFile(filePath);
      }
    }
  }
  
  scanMarkdownFiles('src/content/docs');
  return fixedCount;
}

// 主函数
function main() {
  console.log('🔍 Scanning for corrupted image files...\n');
  
  const corruptedFiles = findCorruptedImages();
  
  if (corruptedFiles.length === 0) {
    console.log('✅ No corrupted image files found!');
    return;
  }
  
  console.log(`Found ${corruptedFiles.length} corrupted image files:`);
  corruptedFiles.forEach(file => {
    console.log(`  - ${file}`);
  });
  
  console.log('\n🗑️  Removing corrupted files...');
  let removedCount = 0;
  for (const file of corruptedFiles) {
    try {
      fs.unlinkSync(file);
      console.log(`✅ Removed: ${file}`);
      removedCount++;
    } catch (error) {
      console.error(`❌ Failed to remove ${file}:`, error.message);
    }
  }
  
  console.log('\n📝 Commenting out references in markdown files...');
  const fixedReferences = commentOutImageReferences(corruptedFiles);
  
  console.log(`\n📊 Summary:`);
  console.log(`   Corrupted files found: ${corruptedFiles.length}`);
  console.log(`   Files removed: ${removedCount}`);
  console.log(`   Markdown files fixed: ${fixedReferences}`);
}

main();
