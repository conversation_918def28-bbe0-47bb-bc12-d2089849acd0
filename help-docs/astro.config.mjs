import starlight from '@astrojs/starlight';
import { defineConfig } from 'astro/config';

// https://astro.build/config
export default defineConfig({
	integrations: [
		starlight({
			title: '升辉ERP帮助文档',
			components: {
				// 使用自定义搜索组件
				Search: './src/components/CustomSearch.astro',
				// 使用自定义页面导航组件
				Pagination: './src/components/PageNavigation.astro',
				// 使用自定义页面组件（集成面包屑和返回顶部）
				Page: './src/components/Page.astro',
			},
			// 移除区域设置，直接使用默认文档
			defaultLocale: 'root',
			locales: {
				// 使用根目录作为文档来源
				root: {
					label: '简体中文',
					lang: 'zh-CN',
				},
			},
			sidebar: [
				{
					label: '概要',
					items: [
						{ label: '长图介绍', slug: 'overview/long-image' },
						{ label: '系统演示', slug: 'overview/demo' },
					],
				},
				{
					label: '第一章 系统设置',
					items: [
						{
							label: '组织管理', items: [
								{ label: '部门管理', slug: 'system/organizational/department' },
								{ label: '角色管理', slug: 'system/organizational/role' },
								{ label: '员工管理', slug: 'system/organizational/employee' },
								{ label: '登录日志', slug: 'system/organizational/login-log' },
							]
						},
						{
							label: '店铺装修', items: [
								{ label: '启动页', slug: 'system/shop-decoration/start-page' },
								{ label: '风格设置', slug: 'system/shop-decoration/style-settings' },
								{ label: '分类模板', slug: 'system/shop-decoration/category-template' },
								{ label: '页面设计', slug: 'system/shop-decoration/page-design' },
							]
						},
						{
							label: '公告设置', items: [
								{ label: '公告设置', slug: 'system/notice-settings' },
							]
						},
						{
							label: '微信小程序',
							items: [
								{ label: '小程序设置', slug: 'system/wx-mp/mp-settings' },
								{ label: '小程序发布', slug: 'system/wx-mp/mp-publish' },
							],
						},
						{
							label: '其它设置',
							items: [
								{ label: '系统设置', slug: 'system/base/settings' },
								{ label: '语音设置', slug: 'system/base/voice-settings' },
								{ label: '消息推送', slug: 'system/base/push-notification' },
								{ label: '流程设置', slug: 'system/other/process-settings' },
								{ label: '支付设置', slug: 'system/other/pay-settings' },
								{ label: '配送设置', slug: 'system/other/delivery-settings' },
								{ label: '司机列表', slug: 'system/other/driver-list' },
								{ label: '线路列表', slug: 'system/other/route-settings' },
							],
						}
					],
				},
				{
					label: '第二章 商品',
					items: [
						{ label: '商品管理', 
							items: [
								{ label: '商品列表', slug: 'goods/manage/publish-goods' },
								{ label: '商品资料', slug: 'goods/manage/base-data-list' },
								{ label: '商品分类', slug: 'goods/manage/goods-classify' },
								{ label: '商品品牌', slug: 'goods/manage/brand-manage' },
								{ label: '单位管理', slug: 'goods/manage/unit-measurement' },
								{ label: '属性管理', slug: 'goods/manage/spec-manage' },
								{ label: '商品服务', slug: 'goods/manage/goods-emprego' },
								{ label: '商品分组', slug: 'goods/manage/goods-grouping' }
							]
						},
						{ label: '价格管理',
							items: [
								{ label: '价格表', slug: 'goods/price/price-table' },
								{ label: '调价单', slug: 'goods/price/adjust-price-goods' },
							]
						},
					],
				},
				{
					label: '第三章 订单管理',
					items: [
						{
							label: '订单管理',
							items: [
								{ label: '订货单', slug: 'order/manage/order-list' },
								{ label: '自提单', slug: 'order/manage/self-pickup' },
								{ label: '退货单', slug: 'order/manage/return-order' },
								{ label: '代客下单', slug: 'order/manage/proxy-order' },
							]
						},
						{
							label: '销售报表',
							items: [
								{ label: '商品汇总表', slug: 'order/statement/goods-summary' },
								{ label: '客户汇总表', slug: 'order/statement/customer-summary' },
								{ label: '人员汇总表', slug: 'order/statement/staff-summary' },
							]
						},
					],
				},
				{
					label: '第四章 客户管理',
					items: [
						{
							label: '客户管理',
							items: [
								{ label: '客户列表', slug: 'customer/manage/customer-list' },
								{ label: '客户类型', slug: 'customer/manage/customer-type' },
								{ label: '标签管理', slug: 'customer/manage/label-management' },
							]
						},
						{
							label: '客户审核',
							items: [
								{ label: '未审核', slug: 'customer/check/unaudited' },
								{ label: '待完善资料', slug: 'customer/check/incomplete-data' },
							]
						},
						{
							label: '客户行为',
							items: [
								{ label: '浏览记录', slug: 'customer/behavior/browsing-history' },
								{ label: '购买记录', slug: 'customer/behavior/purchase-history' },
								{ label: '需求提报', slug: 'customer/behavior/demand-report' },
							]
						},
						{
							label: '客户报表',
							items: [
								{ label: '异常客户分析表', slug: 'customer/statement/abnormal-analysis' },
								{ label: '拉新统计', slug: 'customer/statement/new-customer-stats' },
								{ label: '拜访报表', slug: 'customer/statement/visit-report' },
								{ label: '客户分布图', slug: 'customer/statement/customer-map' },
							]
						},
					],
				},
				{
					label: '第五章 采购管理',
					items: [
						{
							label: '采购管理', 
							items: [
								{ label: '采购单', slug: 'purchase/manage/purchase-order' },
								{ label: '采购退货单', slug: 'purchase/manage/purchase-return' },
								{ label: '供应商', slug: 'purchase/manage/supplier' },
								{ label: '缺货单', slug: 'purchase/manage/stockout' },
								{ label: '商户采购单', slug: 'purchase/manage/merchant' },
								{ label: '成本价', slug: 'purchase/manage/cost-price' },
							]
						},
						{
							label: '采购报表',
							items: [
								{ label: '采购明细表', slug: 'purchase/statement/purchase-detail' },
								{ label: '商品汇总表', slug: 'purchase/statement/goods-summary' },
								{ label: '供应商汇总表', slug: 'purchase/statement/supplier-summary' },
								{ label: '采购员汇总表', slug: 'purchase/statement/staff-summary' },
							]
						},
					],
				},
				{
					label: '第六章 库存系统',
					// autogenerate: { directory: 'stock' },
					items: [
						{
							label: '出入库管理',
							items: [
								{ label: '入库管理', slug: 'stock/section1/in' },
								{ label: '出库管理', slug: 'stock/section1/out' },
							]
						},
						{
							label: '库存管理',
							items: [
								{ label: '库存查询', slug: 'stock/section2/query' },
								{ label: '仓库库存', slug: 'stock/section2/warehouse-inventory' },
								{ label: '库存汇总', slug: 'stock/section2/inventory-summary' },
								{ label: '库存流水', slug: 'stock/section2/flow' },
								{ label: '批次流水', slug: 'stock/section2/batch' },
								{ label: '保质期查询', slug: 'stock/section2/expiry' },
							]
						},
						{
							label: '仓库管理',
							items: [
								{ label: '调拨单', slug: 'stock/section3/allocate' },
								{ label: '盘点单', slug: 'stock/section3/stocktake' },
								{ label: '报损单', slug: 'stock/section3/loss-report' },
								{ label: '仓库管理', slug: 'stock/section3/warehouse' },
								{ label: '库区管理', slug: 'stock/section3/warehouse-area' },
								{ label: '库位管理', slug: 'stock/section3/warehouse-location' },
							]
						},
						{
							label: '供应商',
							items: [
								{ label: '供应商库存', slug: 'stock/supplier/supplier-inventory' },
								{ label: '库存流水', slug: 'stock/supplier/supplier-inventory-flowing' },
							]
						},
					],
				},
				{
					label: '第七章 财务',
					items: [
						{
							label: '应收管理',
							items: [
								{ label: '客户往来汇总表', slug: 'finance/section1/customer-summary' },
								{ label: '应收单列表', slug: 'finance/section1/receivables' },
								{ label: '收款单列表', slug: 'finance/section1/receipts' },
								{ label: '收款申请单', slug: 'finance/section1/apply-receipt' },
								{ label: '客户往来明细表', slug: 'finance/section1/customer-balance-detail' },
								{ label: '销售退款单', slug: 'finance/section1/sales-refund' },
							]
						},
						{
							label: '应付管理',
							items: [
								{ label: '供应商往来汇总表', slug: 'finance/section2/supplier-summary' },
								{ label: '应付单列表', slug: 'finance/section2/payables' },
								{ label: '付款单列表', slug: 'finance/section2/payments' },
								{ label: '供应商余额表', slug: 'finance/section2/supplier-balance' },
								{ label: '供应商往来明细表', slug: 'finance/section2/supplier-balance-detail' },
							]
						},
						{
							label: '出纳管理',
							items: [
								{ label: '资金转账单', slug: 'finance/section3/fund-transfer' },
								{ label: '账户管理', slug: 'finance/section3/account-management' },
								{ label: '账户明细查询', slug: 'finance/section3/account-detail-query' },
							]
						},
						{
							label: '财务管理',
							items: [
								{ label: '费用单', slug: 'finance/section4/expense-order' },
								{ label: '财务类型', slug: 'finance/section4/finance-types' },
								{ label: '余额提现', slug: 'finance/section4/balance-withdrawal' },
								{ label: '费用类型', slug: 'finance/section4/expense-types' },
								// { label: '供应商付款单列表', slug: 'finance/section4/supplier-payment-list' },
							]
						},
					],
				},
				{
					label: '第八章 报表',
					items: [
						{
							label: '商品报表',
							items: [
								{ label: '商品销售报表', slug: 'reports/goods-sales-report' },
								{ label: '客户商品报表', slug: 'reports/customer-goods-report' },
							]
						},
						{
							label: '订单报表',
							items: [
								{ label: '客户订单报表', slug: 'reports/customer-order-report' },
								{ label: '订单数据报表', slug: 'reports/order-data-report' },
								{ label: '地区订单报表', slug: 'reports/regional-order-report' },
								{ label: '业务员订单表', slug: 'reports/salesman-order-report' },
							]
						}
					],
				},
				{
					label: '第九章 营销系统',
					items: [
						{
							label: '单据模板',
							items: [
								{ label: '单据模板', slug: 'marketing/bill-template/bill-template' },
							]
						},
						{
							label: '钱货日清对账',
							items: [
								{ label: '库存日对账', slug: 'marketing/money-goods-bill/inventory-daily-reconciliation' },
								{ label: '销售日对账', slug: 'marketing/money-goods-bill/sales-daily-reconciliation' },
								{ label: '财务日对账', slug: 'marketing/money-goods-bill/finance-daily-reconciliation' },
							]
						},
						{
							label: '优惠券',
							items: [
								{ label: '优惠券列表', slug: 'marketing/coupon/coupon-list' },
								{ label: '发放记录', slug: 'marketing/coupon/release-record' },
							]
						},
						{
							label: '促销管理',
							items: [
								{ label: '商品促销', slug: 'marketing/promotion/promotion-list' },
							]
						},
						{
							label: '会员卡',
							items: [
								{ label: '会员卡管理', slug: 'marketing/vip/membership-card' },
								{ label: '领取记录', slug: 'marketing/vip/collection-records' },
							]
						},
						{
							label: '组合套餐',
							items: [
								{ label: '组合套餐', slug: 'marketing/set-meal' },
							]
						},
						{
							label: '积分商城',
							items: [
								{ label: '商品管理', slug: 'marketing/points-mall/goods-manage' },
								{ label: '兑换记录', slug: 'marketing/points-mall/exchange-record' },
								{ label: '积分规则', slug: 'marketing/points-mall/point-rule' },
							]
						},
						{
							label: '满赠活动',
							items: [
								{ label: '满赠活动', slug: 'marketing/full-give/full-give-list' },
							]
						},
						{
							label: '销售提成',
							items: [
								{ label: '提成规则', slug: 'marketing/commission/commission-rule' },
								{ label: '提成统计', slug: 'marketing/commission/commission-statistic' },
							]
						},
						{
							label: '分销',
							items: [
								{ label: '分销商概览', slug: 'marketing/distribution/overview' },
								{ label: '分销商品', slug: 'marketing/distribution/goods-list' },
								{ label: '分销订单', slug: 'marketing/distribution/order-list' },
								{ label: '分销商等级', slug: 'marketing/distribution/distribution-list' },
								{ label: '分销商设置', slug: 'marketing/distribution/settings' },
							]
						},
						{
							label: '车载销售',
							items: [
								{ label: '车辆管理', slug: 'marketing/car-sale/vehicle-list' },
								{ label: '层级管理', slug: 'marketing/car-sale/level-management' },
								{ label: '销售记录', slug: 'marketing/car-sale/sales-record' },
								{ label: '员工结算', slug: 'marketing/car-sale/employee-settlement' }
							]
						}
					],
				},
				{
					label: '第十章 其他相关内容',
					items: [
						{
							label: '微信公众号注册',
							items: [
								{ label: '个体户注册公众平台步骤', slug: 'other/section1/individual-registration' },
								{ label: '企业注册公众平台步骤', slug: 'other/section1/enterprise-registration' },
							]
						},
						{
							label: '微信公众号认证流程',
							items: [
								{ label: '微信认证流程（个体户）', slug: 'other/section2/individual-verification' },
								{ label: '微信认证流程（企业）', slug: 'other/section2/enterprise-verification' },
							]
						},
						{
							label: '微信小程序注册及认证',
							items: [
								{ label: '已认证公众号申请小程序流程', slug: 'other/section3/mini-program-application' },
							]
						},
						{ label: 'VIP全包介绍', slug: 'other/vip-package' },
						{ label: '图片介绍', slug: 'other/image-introduction' },
						{ label: '微信开放平台如何绑定小程序', slug: 'other/bind-mini-program' },
					],
				},
			],
		}),
	],
});


