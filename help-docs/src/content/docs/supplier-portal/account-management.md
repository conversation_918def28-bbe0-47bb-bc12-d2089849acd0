---
title: 供应商账户管理
description: 供应商账户管理功能，包括密码修改和账户安全设置
---

# 供应商账户管理

供应商账户管理是供应商角色端唯一的操作功能，为供应商提供密码修改和基本的账户安全管理服务。

## 功能概述

账户管理功能提供：
- 密码修改功能
- 账户安全设置
- 登录历史查看
- 安全退出功能

## 访问路径

1. 登录供应商角色端
2. 在左侧菜单中点击**账户管理**
3. 进入账户管理页面

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![账户管理主界面](../../../assets/supplier-portal/account-management-main.png) --> --> --> --> --> --> --> --> -->

## 主要功能

### 密码修改

#### 修改步骤
1. 在账户管理页面点击**修改密码**按钮
2. 在弹出的对话框中填写密码信息
3. 完成验证后提交修改

#### 密码修改表单

密码修改需要填写以下信息：

| 字段名称 | 说明 | 要求 |
|---------|------|------|
| 原密码 | 当前使用的密码 | 必填，用于身份验证 |
| 新密码 | 要设置的新密码 | 必填，需符合安全要求 |
| 确认密码 | 再次输入新密码 | 必填，需与新密码一致 |

![密码修改对话框](../../../assets/supplier-portal/account-management-password-dialog.png)

#### 密码安全要求

新密码必须满足以下安全要求：
- **长度要求**：至少8位字符
- **复杂度要求**：包含大写字母、小写字母、数字
- **特殊字符**：建议包含特殊字符（!@#$%^&*）
- **历史密码**：不能与最近3次使用的密码相同

#### 修改流程
1. **原密码验证**：系统验证原密码是否正确
2. **新密码检查**：检查新密码是否符合安全要求
3. **确认密码验证**：确认两次输入的新密码一致
4. **密码更新**：更新密码并记录修改时间
5. **强制重新登录**：修改成功后自动退出，需重新登录

![密码修改成功提示](../../../assets/supplier-portal/account-management-password-success.png)

### 账户信息查看

#### 基本信息展示
账户管理页面显示以下基本信息：
- **供应商名称**：当前登录的供应商名称
- **登录账号**：用于登录的账号名称
- **最后登录时间**：上次登录的时间
- **密码修改时间**：最近一次修改密码的时间

![账户基本信息](../../../assets/supplier-portal/account-management-basic-info.png)

#### 账户状态
显示账户的当前状态：
- **正常**：账户状态正常，可正常使用
- **锁定**：账户被锁定，需联系管理员
- **过期**：账户已过期，需要续期

### 登录历史记录

#### 历史记录查看
1. 在账户管理页面点击**登录历史**标签
2. 查看最近的登录记录
3. 了解账户的使用情况

#### 历史记录信息

登录历史记录包含以下信息：

| 字段名称 | 说明 | 示例 |
|---------|------|------|
| 登录时间 | 登录的具体时间 | 2025-01-21 09:30:25 |
| 登录IP | 登录时使用的IP地址 | ************* |
| 浏览器 | 使用的浏览器类型 | Chrome 120.0 |
| 操作系统 | 登录设备的操作系统 | Windows 10 |
| 登录状态 | 登录是否成功 | 成功/失败 |

![登录历史记录](../../../assets/supplier-portal/account-management-login-history.png)

### 安全设置

#### 安全提醒设置
- **密码过期提醒**：密码即将过期时的提醒设置
- **异常登录提醒**：检测到异常登录时的提醒
- **账户锁定提醒**：账户被锁定时的通知设置

#### 会话管理
- **自动退出时间**：无操作自动退出的时间设置
- **强制单点登录**：是否允许多地同时登录
- **记住登录状态**：是否记住登录状态

![安全设置选项](../../../assets/supplier-portal/account-management-security-settings.png)

## 安全功能

### 密码强度检测
系统实时检测密码强度：
- **弱**：红色显示，不符合安全要求
- **中**：黄色显示，基本符合要求
- **强**：绿色显示，符合所有安全要求

### 登录异常检测
系统自动检测以下异常情况：
- 异常IP地址登录
- 异常时间段登录
- 多次登录失败
- 异常设备登录

### 账户保护机制
- **登录失败锁定**：连续5次登录失败自动锁定账户
- **密码过期提醒**：密码90天未修改时提醒更新
- **会话超时**：30分钟无操作自动退出
- **强制重新认证**：敏感操作需要重新输入密码

## 使用建议

### 密码安全建议
1. **定期修改**：建议每3个月修改一次密码
2. **复杂密码**：使用包含多种字符类型的复杂密码
3. **唯一密码**：不要在其他系统中使用相同密码
4. **保密存储**：不要将密码告诉他人或写在明显位置

### 账户安全建议
1. **及时退出**：使用完毕后及时安全退出
2. **检查记录**：定期查看登录历史，发现异常及时处理
3. **更新浏览器**：使用最新版本的浏览器访问系统
4. **安全网络**：避免在公共网络环境下登录

## 常见问题

### Q: 忘记原密码怎么办？
A: 
- 供应商角色端不提供密码重置功能
- 请联系系统管理员协助重置密码
- 重置后建议立即修改为新密码

### Q: 密码修改后无法登录怎么办？
A: 
- 确认新密码输入是否正确
- 检查键盘大小写锁定状态
- 如仍无法登录，请联系系统管理员

### Q: 如何提高账户安全性？
A: 
- 使用复杂密码并定期修改
- 不在公共设备上登录
- 及时退出系统
- 定期查看登录历史

## 注意事项

- 密码修改后需要重新登录所有设备
- 系统会记录所有密码修改操作
- 连续登录失败会导致账户锁定
- 建议在安全的网络环境下进行密码修改
- 如发现账户异常，请立即联系系统管理员

---

**文档版本**：v1.0  
**创建时间**：2025-01-21  
**最后更新**：2025-01-21
