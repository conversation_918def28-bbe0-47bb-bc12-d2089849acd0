---
title: 商品管理
description: 升辉ERP商品管理功能详细介绍，包括商品列表查看、商品分类管理等功能
---

# 商品管理

升辉ERP的商品管理模块为用户提供了全面的商品信息管理功能，包括商品列表查看、商品分类管理、商品信息编辑等核心功能。

## 功能概述

商品管理模块主要包含以下功能：

- **商品列表**：查看和管理所有商品信息
- **商品分类**：管理商品分类体系
- **商品搜索**：支持多种条件的商品搜索
- **批量操作**：支持批量上下架、设置、转移等操作

## 商品列表功能

### 访问路径

1. 登录升辉ERP系统
2. 点击顶部导航栏的**商品**标签页
3. 选择**商品列表**子标签页

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![商品列表主界面](../../../assets/supplier-portal/goods-list-main.png) --> --> --> --> --> --> --> --> -->

*图：商品列表主界面，展示了商品的详细信息包括编码、名称、分类、规格、库存等*

### 主要功能

#### 1. 商品信息展示

商品列表以表格形式展示商品的关键信息：

- **编码**：商品的唯一标识码（如：MATERIEL002505）
- **编码（旧）**：历史编码信息
- **商品**：商品名称和缩略图
- **商品分类**：商品所属分类
- **规格**：商品规格信息（如：15种规格）
- **基本单位**：商品的计量单位（副、只、套、个等）
- **排序**：商品排序权重
- **总库存**：当前库存数量
- **真实销量**：实际销售数量
- **销售状态**：上架/下架状态
- **销售店铺**：销售渠道
- **创建时间**：商品创建时间

#### 2. 搜索和筛选功能

系统提供多种搜索和筛选条件：

- **商品名称/商品编码**：支持模糊搜索
- **商品分类**：按分类筛选商品
- **商品品牌**：按品牌筛选
- **销售状态**：筛选上架/下架商品
- **销售店铺**：按店铺筛选

#### 3. 状态标签页

商品列表提供四个状态标签页：

- **全部**：显示所有商品
- **销售中**：显示正在销售的商品
- **已售罄**：显示库存为0的商品
- **仓库中**：显示仓库中的商品

#### 4. 商品操作功能

每个商品支持以下操作：

- **编辑**：修改商品信息
- **删除**：删除商品（谨慎操作）
- **置顶/取消置顶**：调整商品显示优先级
- **上架/下架**：通过开关控制商品销售状态

#### 5. 批量操作功能

系统支持多种批量操作：

- **批量上下架**：批量修改商品销售状态
- **批量设置**：批量修改商品属性
- **批量转移**：批量转移商品到其他分类或店铺
- **批量负库存销售**：设置负库存销售权限
- **批量删除**：批量删除选中商品

### 分页和显示设置

- **分页显示**：支持分页浏览，默认每页显示10条记录
- **页面跳转**：支持直接跳转到指定页面
- **显示数量设置**：可调整每页显示的商品数量

## 商品分类管理

### 访问路径

1. 在商品管理页面
2. 点击**商品分类**标签页

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![商品分类管理界面](../../../assets/supplier-portal/goods-category-main.png) --> --> --> --> --> --> --> --> -->

*图：商品分类管理界面，展示了分类名称、图片、编码、商品数量、状态和排序等信息*

### 主要功能

#### 1. 分类信息展示

商品分类以表格形式展示分类的详细信息：

- **分类名称**：分类的名称（如：充电器、电机轮毂、铁件系列等）
- **图片**：分类的缩略图
- **分类编码**：分类的唯一标识码（如：CATEGORY000519）
- **商品数量**：该分类下的商品数量
- **分类状态**：启用/禁用状态（通过开关控制）
- **排序**：分类显示的排序权重

#### 2. 分类操作功能

每个分类支持以下操作：

- **新建下级**：在当前分类下创建子分类
- **编辑**：修改分类信息
- **删除**：删除分类（需确保分类下无商品）

#### 3. 分类管理特点

- **层级结构**：支持多级分类管理
- **状态控制**：可启用/禁用分类
- **排序管理**：通过排序值控制分类显示顺序
- **商品统计**：实时显示每个分类下的商品数量

### 典型分类示例

系统中包含多种商品分类，如：

- **充电器**（6个商品）
- **电机轮毂**（11个商品）
- **铁件系列**（111个商品）
- **车筐 鞍座 靠背**（13个商品）
- **刹车部件**（70个商品）
- **灯具仪表**（23个商品）
- **电子电器/电线插座**（53个商品）
- 等等...

## 使用建议

### 日常管理建议

1. **定期检查库存**：关注库存数量，及时补货
2. **优化商品信息**：完善商品描述、图片等信息
3. **合理分类管理**：保持分类结构清晰合理
4. **监控销售状态**：及时调整商品上下架状态

### 搜索技巧

1. **组合搜索**：结合多个搜索条件提高查找效率
2. **状态筛选**：使用状态标签页快速定位特定状态商品
3. **排序功能**：利用排序功能优化商品展示顺序

## 注意事项

- 删除商品操作不可恢复，请谨慎操作
- 批量操作前请仔细确认选择的商品
- 商品编码具有唯一性，不可重复
- 修改商品信息后需要一定时间同步到前台展示

## 权限说明

不同角色用户对商品管理功能的权限不同：

- **管理员**：拥有所有商品管理权限
- **普通用户**：可能只有查看和部分编辑权限
- **供应商角色**：通常只有查看权限，无法修改商品信息

---

**文档版本**：v1.0  
**创建时间**：2025-01-21  
**适用版本**：升辉ERP v2.0+
