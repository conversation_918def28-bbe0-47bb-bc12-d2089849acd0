---
title: 库存状态查询
description: 供应商当前库存状态查询功能，实时查看商品库存数量和仓库位置信息
---

# 库存状态查询

库存状态查询功能为供应商提供实时的库存信息查询服务，帮助供应商掌握商品在仓库中的当前状态。

## 功能概述

库存状态查询功能提供：
- 实时库存数量查询
- 仓库位置信息展示
- 库存更新时间追踪
- 多维度搜索和筛选

## 访问路径

1. 登录供应商角色端
2. 在左侧菜单中点击**库存状态**
3. 进入库存状态查询页面

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![库存状态查询主界面](../../../assets/supplier-portal/inventory-status-main.png) --> --> --> --> --> --> --> --> -->

## 主要功能

### 库存列表查询

#### 显示字段

库存状态列表包含以下信息：

| 字段名称 | 说明 | 示例 |
|---------|------|------|
| 商品名称 | 商品的完整名称 | iPhone 15 Pro Max |
| SKU | 商品的唯一标识码 | IP15PM-256G-BLK |
| 当前库存 | 商品的实时库存数量 | 150 |
| 仓库位置 | 商品存放的具体位置 | A区-01-03 |
| 更新时间 | 库存信息最后更新时间 | 2025-01-21 15:30:25 |

<!-- ![库存状态列表](../../../assets/supplier-portal/inventory-status-list.png) -->

#### 库存状态标识

系统使用颜色标识不同的库存状态：

- **绿色**：库存充足（数量 > 100）
- **黄色**：库存偏低（数量 20-100）
- **红色**：库存不足（数量 < 20）
- **灰色**：无库存（数量 = 0）

### 搜索和筛选功能

#### 商品名称搜索
1. 在**商品名称**搜索框中输入关键词
2. 支持模糊搜索，可输入商品名称的部分内容
3. 点击**查询**按钮或按回车键执行搜索

<!-- <!-- ![商品名称搜索](../../../assets/supplier-portal/inventory-status-search.png) --> -->

#### SKU搜索
1. 在**SKU**搜索框中输入商品编码
2. 支持精确匹配和模糊搜索
3. 快速定位特定商品的库存信息

#### 仓库位置筛选
1. 点击**仓库位置**下拉框
2. 选择要查看的仓库区域
3. 系统筛选该区域的库存信息

![仓库位置筛选](../../../assets/supplier-portal/inventory-status-location-filter.png)

#### 库存状态筛选
1. 点击**库存状态**筛选按钮
2. 选择要查看的库存状态（充足/偏低/不足/无库存）
3. 快速筛选特定状态的商品

### 库存详情查看

#### 详情操作
1. 在库存列表中找到目标商品
2. 点击该行的**详情**按钮
3. 弹出库存详情对话框

#### 详情内容
库存详情包含以下信息：
- 商品基本信息（名称、SKU、规格等）
- 详细库存分布（按仓库位置）
- 最近入库/出库记录
- 库存变化趋势图表

<!-- ![库存详情对话框](../../../assets/supplier-portal/inventory-status-detail.png) -->

### 库存分布查看

#### 多仓库显示
如果商品分布在多个仓库位置：
1. 详情页面显示各位置的库存数量
2. 提供库存分布饼图
3. 支持按位置查看详细信息

#### 批次信息
对于有批次管理的商品：
- 显示各批次的库存数量
- 展示批次的生产日期和有效期
- 提供批次库存的详细分布

![库存分布信息](../../../assets/supplier-portal/inventory-status-distribution.png)

## 高级功能

### 实时刷新
- 页面数据每2分钟自动刷新
- 点击**刷新**按钮立即更新数据
- 显示最后更新时间

### 库存预警
系统自动标识需要关注的库存状态：
- **库存不足预警**：红色标识低库存商品
- **零库存提醒**：特别标注无库存商品
- **长期无变动**：标识长时间无出入库的商品

![库存预警标识](../../../assets/supplier-portal/inventory-status-alerts.png)

### 数据导出
1. 设置好查询条件并执行查询
2. 点击**导出**按钮
3. 选择导出格式（Excel/CSV）
4. 系统生成包含库存信息的文件

### 快速筛选
- **全部库存**：显示所有商品的库存信息
- **有库存**：只显示库存数量大于0的商品
- **低库存**：显示库存不足的商品
- **零库存**：显示无库存的商品

![快速筛选选项](../../../assets/supplier-portal/inventory-status-quick-filter.png)

## 权限说明

### 数据范围
- 只能查看当前供应商的库存信息
- 严格的数据隔离，无法查看其他供应商库存
- 不显示库存成本和价值信息

### 操作限制
- 所有库存信息均为只读模式
- 无法修改库存数量
- 无法执行库存调整操作

## 数据准确性

### 更新频率
- 库存数据实时同步
- 入库/出库操作后立即更新
- 数据延迟通常不超过2分钟

### 数据来源
- 库存数据来源于仓库管理系统
- 基于实际的入库/出库单据计算
- 定期与实物盘点结果核对

## 使用建议

1. **定期查看**：建议每日查看库存状态，及时了解库存变化
2. **关注预警**：重点关注低库存和零库存商品
3. **核对数据**：定期核对系统库存与实际库存
4. **及时沟通**：发现异常及时与仓库管理人员沟通

## 常见问题

### Q: 为什么库存数量与预期不符？
A: 可能的原因：
- 有未处理完成的入库/出库单据
- 系统数据同步存在延迟
- 存在库存调整或盘点操作
- 建议联系仓库管理员核实

### Q: 库存信息多久更新一次？
A: 
- 实时更新：入库/出库操作后立即更新
- 自动刷新：页面每2分钟自动刷新
- 手动刷新：点击刷新按钮立即更新

### Q: 如何处理库存异常？
A: 
- 库存查询功能仅提供查看功能
- 如发现库存异常，请联系仓库管理员
- 库存调整需要通过正式流程处理

## 注意事项

- 库存数据仅供参考，实际库存以仓库实物为准
- 系统维护期间可能出现数据暂时不可用
- 建议定期核对库存数据，确保准确性
- 如发现数据异常，请及时联系系统管理员

---

**文档版本**：v1.0  
**创建时间**：2025-01-21  
**最后更新**：2025-01-21
