---
title: 分账明细功能
description: 供应商分账明细查询和详情查看功能，支持规则快照展示和透明化分账流程
---

# 分账明细功能

分账明细功能为供应商提供透明的分账信息查询服务，包括分账明细列表查看、详情查询和规则快照展示，确保分账流程的透明度和可追溯性。

## 功能概述

分账明细功能基于现有分账系统，为供应商提供：
- 分账明细列表查询
- 分账明细详情查看
- 规则快照展示功能
- 历史分账记录追溯

## 访问路径

1. 登录供应商角色端
2. 在左侧菜单中点击**分账明细**
3. 进入分账明细查询页面

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![分账明细查询主界面](../../../assets/supplier-portal/settlement-details-main.png) --> --> --> --> --> --> --> --> -->

## 主要功能

### 分账明细列表

#### 显示字段

分账明细列表包含以下信息：

| 字段名称 | 说明 | 示例 |
|---------|------|------|
| 结算时间 | 分账明细生成时间 | 2025-01-21 14:30:25 |
| 商品名称 | 分账商品的完整名称 | iPhone 15 Pro Max |
| SKU | 商品的唯一标识码 | IP15PM-256G-BLK |
| 出库数量 | 本次分账的出库数量 | 15 |
| 固定单价 | 商品的固定结算单价 | ¥8,500.00 |
| 结算金额 | 本次分账的结算金额 | ¥127,500.00 |
| 状态 | 分账明细的处理状态 | 已分账 |

<!-- ![分账明细列表](../../../assets/supplier-portal/settlement-details-list.png) -->

#### 状态说明

分账状态包括以下几种：

- **待分账**：已生成明细，等待分账处理
- **已分账**：分账完成，资金已分配
- **分账中**：正在处理分账流程
- **已取消**：分账明细已取消

### 搜索和筛选功能

#### 时间范围筛选
1. 点击**结算时间**筛选框
2. 选择开始日期和结束日期
3. 点击**查询**按钮应用筛选条件

<!-- ![时间范围筛选](../../../assets/supplier-portal/settlement-details-date-filter.png) -->

#### 商品名称搜索
1. 在**商品名称**搜索框中输入关键词
2. 支持模糊搜索，可输入商品名称的部分内容
3. 点击**查询**按钮执行搜索

#### 状态筛选
1. 点击**分账状态**下拉框
2. 选择要查看的分账状态
3. 系统自动筛选符合条件的记录

<!-- ![状态筛选](../../../assets/supplier-portal/settlement-details-status-filter.png) -->

### 分账明细详情

#### 详情查看操作
1. 在分账明细列表中找到目标记录
2. 点击该行的**详情**按钮
3. 弹出分账明细详情对话框

#### 基本信息展示
分账明细详情包含以下基本信息：
- 分账明细编号和生成时间
- 商品基本信息（名称、SKU、规格等）
- 分账计算信息（数量、单价、金额）
- 分账状态和处理时间

![分账明细基本信息](../../../assets/supplier-portal/settlement-details-basic-info.png)

### 规则快照展示

#### 规则快照功能
规则快照保存了分账时的完整规则信息，确保历史数据的审计追溯能力：

1. 在分账明细详情中点击**规则快照**标签
2. 查看分账时使用的规则配置
3. 了解分账计算的具体依据

#### 规则快照内容

规则快照以表格形式展示SKU信息：

| 字段名称 | 说明 | 示例 |
|---------|------|------|
| 商品编码 | 商品的SKU编码 | IP15PM-256G-BLK |
| 商品名称 | 商品的完整名称 | iPhone 15 Pro Max |
| 规格组详情 | 商品的规格信息 | 256GB 黑色 |
| 单位名称 | 商品的计量单位 | 台 |
| 固定金额 | 分账的固定金额 | ¥8,500.00 |

![规则快照表格](../../../assets/supplier-portal/settlement-details-rule-snapshot.png)

#### 规则快照特性

1. **展开/收起功能**：
   - 当SKU数量超过10条时，默认只显示前10条
   - 点击**展开更多**按钮查看全部记录
   - 点击**收起**按钮折叠显示

2. **空数据处理**：
   - 当没有规则快照数据时，显示友好提示
   - 提示信息："暂无规则快照数据"

3. **数据完整性**：
   - 规则快照包含分账时的完整规则配置
   - 确保历史数据的准确性和可追溯性

![规则快照展开功能](../../../assets/supplier-portal/settlement-details-rule-expand.png)

## 高级功能

### 分页浏览
- 系统自动分页显示分账明细
- 每页显示20条记录
- 使用页面底部的分页控件切换页面

### 数据导出
1. 设置好查询条件并执行查询
2. 点击**导出**按钮
3. 选择导出格式（Excel/CSV）
4. 系统生成包含分账明细的文件

### 快速筛选
- **本月分账**：显示当月的分账明细
- **上月分账**：显示上月的分账明细
- **待处理**：只显示待分账的明细
- **已完成**：只显示已完成分账的明细

![快速筛选选项](../../../assets/supplier-portal/settlement-details-quick-filter.png)

### 金额格式化

系统统一使用标准的金额格式化：
- 保持2位小数显示
- 使用千分位分隔符
- 不显示货币符号（与现有组件保持一致）

示例：`127,500.00`

## 权限说明

### 数据范围
- 只能查看当前供应商的分账明细
- 严格的数据隔离，无法查看其他供应商分账
- 不显示销售价格和利润相关信息

### 操作限制
- 所有分账明细均为只读模式
- 无法修改分账金额或状态
- 无法执行分账操作

## 分账流程说明

### 分账生成
1. 系统根据出库记录自动生成分账明细
2. 使用固定金额计算方式进行分账
3. 保存完整的分账规则快照

### 分账处理
1. 系统自动处理分账计算
2. 生成分账明细记录
3. 更新分账状态为"已分账"

### 规则快照保存
1. 分账时保存完整的规则配置
2. 包含审计信息：规则ID、规则内容、快照时间
3. 确保历史数据的可追溯性

## 使用建议

1. **定期查看**：建议定期查看分账明细，了解分账情况
2. **核对金额**：仔细核对分账金额是否正确
3. **查看快照**：通过规则快照了解分账计算依据
4. **保存记录**：重要的分账记录建议导出保存

## 常见问题

### Q: 为什么某些出库记录没有生成分账明细？
A: 可能的原因：
- 商品尚未配置分账规则
- 出库单据尚未完成审核流程
- 分账规则尚未生效

### Q: 规则快照显示为空是什么原因？
A: 可能的原因：
- 分账时未正确保存规则快照
- 规则配置数据异常
- 建议联系系统管理员核实

### Q: 分账金额计算有误怎么办？
A: 
- 请查看规则快照中的固定金额配置
- 确认出库数量是否准确
- 如有疑问请联系系统管理员核实

## 注意事项

- 分账明细数据延迟通常不超过30分钟
- 规则快照确保历史数据的准确性
- 如发现分账异常，请及时联系系统管理员
- 建议定期核对分账记录，确保准确性

---

**文档版本**：v1.0  
**创建时间**：2025-01-21  
**最后更新**：2025-01-21
