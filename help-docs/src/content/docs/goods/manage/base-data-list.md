---
title: 商品资料
description: 学习如何管理商品基础资料，包括新增、编辑、删除商品以及设置商品属性等功能
---

# 商品资料管理

商品资料管理是ERP系统的基础功能，用于维护所有商品的基础信息。通过商品资料管理，您可以创建商品档案、设置商品属性、上传商品图片，为后续的销售和库存管理提供数据基础。

<!-- <!-- ![商品资料列表主界面](/assets/images/goods/manage/goods-data-list.png) --> -->

## 功能特点

1. **完整的商品档案**：支持商品基本信息、图片、属性、多语言等全方位管理
2. **灵活的分类体系**：支持主分类和副分类的层级管理
3. **丰富的属性设置**：可自定义商品属性，满足不同行业需求
4. **多媒体支持**：支持商品图片、视频上传和富文本详情编辑
5. **多语言支持**：支持多语言商品信息，适应国际化需求

## 访问路径

1. 在左侧导航菜单中，点击**商品**
2. 在展开的子菜单中，选择**商品资料**

## 界面概述

主要功能区域包括：

1. 搜索筛选区
   - 商品名称：输入商品名称进行搜索
   - 商品分类：选择特定的商品分类进行筛选
   - 商品品牌：按品牌筛选商品
   - 商品状态：查看不同状态的商品
   - 商户：按商户筛选商品

2. 列表显示区
   - ID：商品唯一标识
   - 编码：商品编码
   - 商品：商品名称和图片
   - 商品分类：商品所属分类
   - 商品状态：显示商品当前状态（启用/停用）
   - 商品品牌：商品所属品牌
   - 发布来源：商品的发布渠道
   - 操作：包含编辑、删除、发布等功能按钮

## 操作指南

### 新增商品

#### 1. 商品基本信息

![新增商品表单](/assets/images/goods/manage/add-goods-form.png)

1. 点击右上角的"新增商品"按钮
2. 在新增商品表单中填写以下信息：
   - 商品信息
     * 商品名称：输入商品的名称（必填）
     * 商品分类：选择商品所属的分类（必填）
     * 副分类：选择商品的副分类（可选）
     * 商品品牌：选择商品的品牌
     * 抄码商品：设置是否为抄码商品
     * 关键词：添加商品的关键词标签
   - 编码信息
     * 商品编码：系统自动生成
     * 商品编码(旧)：填写商品的旧编码（可选）
     * 货架编码：填写商品的货架编码
     * 禁止销售店铺：设置禁止销售的店铺
   - 商品单位
     * 基本单位：选择商品的基本计量单位（必填）
     * 商品规格：填写商品规格
     * 商品单重：填写商品重量
     * 默认单位：设置是否为默认单位
3. 点击"下一步"继续填写商品图集和属性信息

#### 2. 商品图集

<!-- ![商品图集](/assets/images/goods/manage/goods-images.png) -->

在商品图集页面，您可以：

1. 商品视频
   - 支持上传商品展示视频
   - 建议尺寸：375px*375px
   - 格式：mp4
   - 大小：建议小于1M

2. 商品图集
   - 支持上传多张商品图片
   - 建议尺寸：375px*375px
   - 格式：jpg、jpeg、png
   - 大小：建议小于1M
   - 点击"+"按钮添加图片

3. 商品详情
   - 使用富文本编辑器编写商品详细描述
   - 支持文字格式化、图片插入等功能
   - 可以通过"图片上传"按钮添加详情图片

4. 完成编辑后点击"下一步"继续设置商品属性，或点击"保存"完成商品创建

#### 3. 商品属性

<!-- ![商品属性](/assets/images/goods/manage/goods-attributes.png) -->

在商品属性页面，您可以：

1. 属性管理
   - 选择已有属性：从下拉列表中选择预设的商品属性
   - 删除属性：点击"删除"按钮移除已选属性
   - 添加属性：点击"添加属性值"新增属性值
   - 添加新属性：点击"新增属性"创建新的属性类型

2. 属性值设置
   - 为每个属性添加具体的属性值
   - 可以添加多个属性值
   - 支持批量添加属性值

3. 操作说明
   - 在设置商品属性之前，建议先在【属性管理】中配置好属性模板
   - 商品属性设置完成后，点击"下一步"进入多语言设置，或点击"保存"完成商品创建
   - 如果商品不需要设置属性，可以直接点击"下一步"或"保存"

#### 4. 多语言设置

<!-- ![多语言设置](/assets/images/goods/manage/goods-languages.png) -->

在多语言设置页面，您可以：

1. 选择支持的语言
   - 勾选需要支持的语言（如：越南语）
   - 系统会自动展开已选语言的设置项

2. 多语言信息设置
   - 商品信息：翻译商品名称等基本信息
   - 商品属性：翻译商品的属性名称和属性值
   - 商品详情：翻译商品的详细描述

3. 操作说明
   - 如果不需要多语言支持，可以直接点击"完成，提交发布"
   - 设置完成后点击"完成，提交发布"按钮完成商品创建
   - 多语言信息可以在创建商品后随时补充和修改

### 编辑商品

1. 在操作列中点击"编辑"按钮
2. 修改需要更新的商品信息
3. 点击保存完成编辑

### 删除商品

1. 在操作列中点击"删除"按钮
2. 在确认弹窗中点击确认

### 发布商品

1. 在操作列中点击"发布"按钮
2. 选择发布渠道
3. 确认发布信息后点击确认

## 注意事项

1. 商品编码一旦创建不可修改
2. 删除商品前请确认该商品没有关联的订单或库存
3. 发布商品前请确保商品信息完整无误
4. 基本单位设置后将影响商品的库存管理和销售计算
5. 上传的图片和视频需符合系统建议的尺寸和大小要求 