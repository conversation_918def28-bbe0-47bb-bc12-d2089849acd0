---
title: 商品分类管理
description: 升辉ERP商品分类管理功能详解，包含分类创建、编辑、删除的详细操作步骤，分类层级关系和排序规则说明
tags: [goods, classify, management, operation, admin, daily-work]
keywords: [商品分类, 分类管理, 商品组织, 分类层级, 排序规则]
---

# 商品分类管理

商品分类是升辉ERP商品管理的基础功能，通过建立科学合理的分类体系，可以帮助您更好地组织和管理商品，提高商品查找效率，优化客户购物体验。系统支持多级分类结构，满足不同行业的分类需求。

## 功能概述

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![商品分类管理主界面](../../../../assets/goods/classify/main.png) --> --> --> --> --> --> --> --> -->

商品分类管理主要包含以下功能：
- **分类列表展示**：以表格形式展示所有商品分类信息
- **多级分类支持**：支持无限级分类层次结构
- **分类信息管理**：包含分类名称、图片、编码、状态等信息
- **排序功能**：支持自定义分类显示顺序
- **批量操作**：支持批量启用/禁用分类
- **客户类型屏蔽**：可设置特定客户类型不显示某些分类

## 页面布局说明

### 主要功能区域

#### 1. 操作工具栏
- **新建分类按钮**：创建新的商品分类
- **面包屑导航**：显示当前页面位置

#### 2. 分类列表表格
显示所有分类的详细信息，包含以下列：

| 列名 | 说明 | 备注 |
|------|------|------|
| 分类名称 | 分类的显示名称 | 支持多语言 |
| 图片 | 分类展示图片 | 可选，用于前端展示 |
| 分类编码 | 系统自动生成的唯一编码 | 格式：CATEGORY000XXX |
| 商品数量 | 该分类下的商品数量 | 实时统计 |
| 分类状态 | 启用/禁用状态 | 可快速切换 |
| 排序 | 分类显示顺序 | 数字越大越靠前 |
| 操作 | 新建下级、编辑、删除 | 根据权限显示 |

## 操作指南

### 新建分类

#### 创建一级分类

1. **点击"新建分类"按钮**
   - 在页面右上角点击"新建分类"按钮
   - 系统弹出新增分类对话框

2. **填写分类基本信息**

   ![新建分类表单](../../../../assets/goods/classify/add-form.png)

   **必填字段**：
   - **分类名称**：输入分类的中文名称
   - **越南语名称**：如需要多语言支持，填写对应语言名称

   **可选字段**：
   - **分类编码**：系统自动生成，无需手动输入
   - **上级分类**：创建一级分类时无需选择
   - **分类图片**：上传分类展示图片（建议尺寸：200x200px）
   - **一级分类广告图**：用于首页或分类页面的广告展示
   - **分类排序**：设置分类显示顺序（默认为0）
   - **是否显示**：控制分类是否在前端显示（默认启用）

3. **设置客户类型屏蔽**（可选）
   - 勾选需要屏蔽的客户类型
   - 被屏蔽的客户类型将无法看到此分类
   - 可选择：公司、山东省、批发、零售等

4. **保存分类**
   - 点击"确定"按钮保存分类
   - 点击"取消"按钮放弃创建

#### 创建下级分类

1. **选择上级分类**
   - 在分类列表中找到要创建下级分类的父分类
   - 点击该分类行的"新建下级"按钮

2. **填写下级分类信息**
   - 系统会自动选择上级分类
   - 填写其他必要信息（与创建一级分类相同）
   - 下级分类会继承上级分类的部分属性

3. **确认创建**
   - 检查信息无误后点击"确定"
   - 新建的下级分类会显示在分类列表中

### 编辑分类

#### 修改分类信息

1. **进入编辑模式**
   - 在分类列表中找到要编辑的分类
   - 点击该分类行的"编辑"按钮

2. **修改分类信息**
   - 在弹出的编辑对话框中修改相关信息
   - 可修改除分类编码外的所有字段
   - 修改上级分类时需注意层级关系

3. **保存修改**
   - 确认修改无误后点击"确定"
   - 修改会立即生效

#### 快速状态切换

1. **启用/禁用分类**
   - 直接点击分类状态列的开关按钮
   - 禁用的分类在前端将不显示
   - 禁用父分类会同时影响所有子分类

### 删除分类

#### 删除条件检查

删除分类前系统会检查以下条件：
- **商品数量**：分类下不能有商品
- **子分类**：不能有下级分类
- **订单关联**：不能有相关订单记录

#### 删除操作步骤

1. **选择要删除的分类**
   - 确保分类满足删除条件
   - 点击该分类行的"删除"按钮

2. **确认删除**
   - 系统弹出确认对话框
   - 仔细阅读删除提示信息
   - 点击"确认"执行删除

3. **删除结果**
   - 删除成功后分类从列表中移除
   - 删除操作不可恢复，请谨慎操作

## 分类层级管理

### 层级结构规则

1. **无限级分类**：系统支持无限级分类层次
2. **父子关系**：每个分类只能有一个父分类
3. **层级显示**：分类列表按层级缩进显示
4. **继承关系**：子分类继承父分类的部分属性

### 层级操作注意事项

1. **移动分类层级**
   - 通过编辑分类修改上级分类
   - 不能将分类设置为自己的子分类
   - 移动后会影响所有子分类

2. **层级权限控制**
   - 不同层级可设置不同的客户类型屏蔽
   - 子分类的权限不能超过父分类

## 排序规则说明

### 排序机制

1. **排序字段**：使用数字进行排序
2. **排序规则**：数字越大排序越靠前
3. **同级排序**：只在同一层级内进行排序
4. **默认排序**：新建分类默认排序为0

### 排序最佳实践

1. **预留空间**：建议使用10、20、30等间隔数字
2. **分类规划**：重要分类使用较大数字
3. **定期整理**：定期调整排序保持合理性

## 注意事项

### 分类命名规范

1. **名称唯一性**：同级分类名称不能重复
2. **命名清晰**：使用简洁明了的分类名称
3. **层级逻辑**：确保分类层级逻辑合理
4. **多语言支持**：如需要，填写对应语言名称

### 图片使用建议

1. **图片格式**：建议使用PNG或JPG格式
2. **图片尺寸**：分类图片建议200x200px
3. **文件大小**：单张图片不超过2MB
4. **图片内容**：使用与分类相关的代表性图片

### 客户类型屏蔽

1. **谨慎设置**：屏蔽设置会影响客户体验
2. **业务逻辑**：确保屏蔽逻辑符合业务需求
3. **定期检查**：定期检查屏蔽设置的合理性

## 常见问题

### Q: 为什么无法删除某个分类？
A: 删除分类需要满足以下条件：
- 分类下没有商品
- 没有下级分类
- 没有相关订单记录
请先处理这些关联数据后再删除。

### Q: 分类排序不生效怎么办？
A: 请检查以下几点：
- 确认排序数字设置正确
- 检查是否在同一层级内
- 刷新页面查看最新排序
- 联系管理员检查权限设置

### Q: 如何批量修改分类状态？
A: 目前系统支持单个分类状态切换，如需批量操作，请：
- 逐个点击状态开关
- 或联系管理员进行批量处理

### Q: 分类图片上传失败怎么办？
A: 请检查以下几点：
- 图片格式是否为PNG或JPG
- 图片大小是否超过2MB
- 网络连接是否正常
- 浏览器是否支持文件上传

## 相关功能

- [商品资料管理](./base-data-list) - 管理具体商品信息
- [商品品牌管理](./brand-manage) - 管理商品品牌
- [商品分组管理](./goods-grouping) - 按业务需求分组商品
- [价格表管理](../price/price-table) - 管理商品价格体系

## 界面概述

<!-- <!-- ![商品分类列表](../../../../assets/goods-data/goods-classify-list.png) --> -->

主要功能区域包括：

1. 分类列表显示区
   - #：序号
   - 分类名称：商品分类的名称
   - 图片：分类的展示图片
   - 分类编码：系统自动生成的唯一编码
   - 商品数量：该分类下的商品数量
   - 分类状态：显示分类是否启用
   - 排序：分类的显示顺序
   - 操作：包含新建下级、编辑、删除等功能按钮

2. 功能按钮区
   - 新建分类：创建新的商品分类
   - 编辑：修改已有分类信息
   - 删除：移除不需要的分类

## 操作指南

### 新建分类

1. 点击右上角的"新建分类"按钮
2. 在弹出的表单中填写：
   - 分类名称
   - 上传分类图片
   - 设置分类状态（启用/停用）
   - 设置排序序号
3. 点击保存完成创建

### 新建下级分类

1. 在操作列中点击"新建下级"按钮
2. 填写下级分类信息
3. 点击保存完成创建

### 编辑分类

1. 在操作列中点击"编辑"按钮
2. 修改需要更新的分类信息
3. 点击保存完成编辑

### 删除分类

1. 在操作列中点击"删除"按钮
2. 在确认弹窗中点击确认

## 注意事项

1. 删除分类前请确保该分类下没有商品
2. 分类编码一旦创建不可修改
3. 建议合理规划分类层级，避免层级过深
4. 分类排序号影响分类在前端的显示顺序
5. 停用分类后，该分类下的商品将不会在前端显示 