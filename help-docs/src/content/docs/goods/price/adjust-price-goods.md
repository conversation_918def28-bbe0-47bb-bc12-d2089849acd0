---
title: 调价单
description: 调价单功能使用指南
---

# 调价单

调价单是用于记录和管理商品价格调整的专用单据，您可以通过调价单来批量或单独调整商品的价格，并追踪价格调整的历史记录。

## 功能介绍

调价单页面展示了所有调价单的列表，包括调价单编码、商品名称、调价日期、调整后价格、调价人、相关商铺以及状态等信息。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![调价单列表页面](../../../../assets/price-adjust/adjust-price-main.png) --> --> --> --> --> --> --> --> --> -->

## 创建调价单

1. 点击调价单列表页面右上角的"新建调价单"按钮
2. 在新建调价单页面，系统会自动生成调价单编号
3. 点击"新增一条"按钮，可以添加多个商品进行批量调价

<!-- <!-- ![新建调价单页面](../../../../assets/price-adjust/adjust-price-create.png) --> -->

## 选择商品

1. 在调价单中添加行项目后，点击商品名称输入框
2. 在弹出的商品列表中，选择需要调价的商品
3. 点击商品对应的"选择商品"按钮，然后点击"确定"按钮

<!-- ![选择商品弹窗](../../../../assets/price-adjust/adjust-price-select-product.png) -->

## 设置调价信息

1. 商品选择完成后，系统会自动加载商品的当前价格信息
2. 在"调整价格"输入框中输入新的价格
3. 您可以设置启用阶梯价和启用多仓库价格的选项

<!-- ![已添加商品的调价单](../../../../assets/price-adjust/adjust-price-with-product.png) -->

## 保存和生效

1. 信息填写完成后，点击页面底部的"保存"按钮提交调价单
2. 调价单创建后，初始状态为"未生效"
3. 在调价单列表页面，点击对应调价单操作栏中的"生效"按钮，使调价单生效
4. 调价单生效后，相关商品的价格将立即更新

<!-- ![调价单生效后状态](../../../../assets/price-adjust/adjust-price-effective.png) -->

## 注意事项

- 调价单生效后，相关商品的价格将立即更新，请谨慎操作
- 可以通过调价单页面查看历史调价记录，方便价格追溯
- 调价单支持多种价格类型的调整，如订货价、销售价等
- 调价单创建后需要手动点击"生效"才能应用价格变更 