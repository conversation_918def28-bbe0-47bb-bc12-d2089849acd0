---
title: 小程序发布
description: 微信小程序发布流程和配置说明
---

小程序发布功能用于配置和管理微信小程序的发布流程，包括基础配置、授权和代码提交等操作。

## 主要功能

### 步骤一：基础配置
配置小程序的基本信息和支付方式：
- 微信小程序配置：确保与微信小程序后台信息一致
- 支付设置：选择微信支付
- 配送方式设置：可以启用适合您商城的配送方式

### 步骤二：小程序授权
完成小程序的授权流程：
- 官方注册小程序：如果未注册，可以通过官方渠道注册
- 授权管理：获取小程序的相关权限
- 小程序授权：立即授权发布功能

### 步骤三：版本发布
选择和提交小程序版本：
- 选择小程序模版版本
- 提交代码进行审核
- 发布新版本

## 操作步骤

1. 完成基础配置：
   - 确认微信小程序配置信息
   - 设置支付方式
   - 配置配送方式
2. 进行小程序授权：
   - 点击"小程序授权"按钮
   - 按照提示完成授权流程
3. 发布新版本：
   - 选择要发布的版本
   - 提交代码审核
   - 等待审核通过后发布

## 界面预览

![小程序发布界面](../../../../assets/system/wechat-miniprogram/miniprogram-publish.png)

## 注意事项

- 发布前请确保所有配置信息准确无误
- 授权过程需要管理员微信扫码确认
- 代码提交后需要等待微信官方审核
- 审核通过后才能发布新版本
- 建议在测试环境充分测试后再提交审核
- 请关注微信官方的审核规范和要求 