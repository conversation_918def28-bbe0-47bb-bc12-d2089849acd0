---
title: 小程序设置
description: 微信小程序基本信息配置和开发者设置
---

小程序设置功能用于配置微信小程序的基本信息和开发者信息，是小程序正常运营的基础配置。

## 主要功能

### 基本信息配置
系统支持配置以下基本信息：
- 小程序名称：小程序的展示名称
- 小程序账号：关联的小程序账号
- 小程序二维码：小程序的识别码
- 小程序原始ID：微信分配的小程序ID

### 开发者设置
开发者相关配置包括：
- AppId：小程序的唯一标识
- AppSecret：小程序的密钥

## 操作步骤

1. 进入小程序设置页面
2. 填写基本信息：
   - 输入小程序名称
   - 填写小程序账号
   - 上传小程序二维码
   - 填写小程序原始ID
3. 配置开发者信息：
   - 输入AppId
   - 输入AppSecret
4. 点击"保存"按钮提交设置

## 界面预览

![小程序设置界面](../../../../assets/system/wechat-miniprogram/miniprogram-settings.png)

## 注意事项

- AppId和AppSecret为敏感信息，请妥善保管
- 小程序名称应与微信审核通过的名称保持一致
- 二维码图片需清晰可识别
- 所有必填项（带*号）都需要填写完整
- 保存成功后，新的配置将立即生效 