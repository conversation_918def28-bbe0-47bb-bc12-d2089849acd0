---
title: 线路列表
---

# 线路列表

## 功能介绍

线路列表模块用于管理配送路线，支持对配送路线进行创建、编辑和删除等操作。通过合理规划配送路线，可以提高配送效率，优化配送资源分配。

## 主要功能

1. 线路查看：显示所有配送线路的基本信息，包括线路ID、线路名称、下单客户数、总单数、订单金额等数据
2. 线路创建：支持新建配送线路，设置线路名称等基本信息
3. 线路编辑：可修改现有线路的相关信息
4. 线路删除：支持删除不再使用的配送线路
5. 查看客户：可查看该线路下的所有客户信息

## 操作步骤

### 1. 查看线路列表

<!-- ![线路列表主页面](../../../../assets/system/other/route-settings/route-list.png) -->

在线路列表页面，您可以：
- 查看所有配送线路的详细信息
- 使用日期筛选查看特定时间段的线路数据
- 查看每条线路的运营数据，如下单客户数、总单数、订单金额等

### 2. 新建线路

1. 点击右上角的【新增】按钮
2. 在弹出的表单中填写以下信息：
   - 线路名称：输入新线路的名称
   - 其他相关信息
3. 点击【确定】保存新建的线路

### 3. 编辑线路

1. 在线路列表中找到需要修改的线路
2. 点击【修改】按钮
3. 在弹出的编辑表单中修改相关信息
4. 点击【确定】保存修改

### 4. 删除线路

1. 在线路列表中找到需要删除的线路
2. 点击【删除】按钮
3. 在弹出的确认框中点击【确定】完成删除

### 5. 查看线路客户

1. 在线路列表中找到目标线路
2. 点击【查看客户】按钮
3. 查看该线路下的所有客户信息

## 注意事项

1. 删除线路前请确保该线路下没有正在进行的配送任务
2. 建议根据实际配送需求合理规划线路
3. 定期检查和优化线路设置，以提高配送效率


