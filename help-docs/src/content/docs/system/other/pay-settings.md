---
title: 支付设置
description: 系统支付方式配置，包括各类支付渠道的启用和管理
---

支付设置功能用于管理系统支持的各种支付方式，您可以根据业务需求启用或禁用不同的支付渠道。

## 主要功能

### 支付方式管理
系统支持以下支付方式：
- 余额支付：使用账户余额支付
- 微信支付：使用微信支付功能
- 支付宝支付：使用支付宝支付功能
- 货到付款：线下现金支付
- 银行打款：银行转账支付

### 支付状态设置
每种支付方式可以设置：
- 启用/禁用状态
- 是否需要认证
- 编辑支付配置
- 设置认证要求

## 操作步骤

1. 进入支付设置页面
2. 查看支付方式列表：
   - ID：支付方式唯一标识
   - 支付方式：支付渠道名称
   - 是否默认：是否为默认支付方式
   - 状态：当前启用状态
3. 管理支付方式：
   - 点击"编辑"修改配置
   - 点击"设为默认"设置默认支付方式
   - 使用开关控制启用状态

## 界面预览

![支付设置界面](../../../../assets/system/settings/payment-settings.png)

## 注意事项

- 至少需要启用一种支付方式
- 微信支付和支付宝支付需要配置相应的商户信息
- 建议根据用户习惯选择合适的支付方式
- 支付方式变更后会即时生效
- 请定期检查支付配置的有效性
- 建议保留多种支付方式以提供备选 