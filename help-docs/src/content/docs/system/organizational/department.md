---
title: 部门管理
description: 学习如何管理企业组织架构中的部门设置，包括部门创建、编辑、删除和层级管理等功能
tags: [system, organization, department, management, hierarchy]
keywords: [部门管理, 组织架构, 部门设置, 层级管理, 人员分配]
---

# 部门管理

部门管理是升辉ERP系统中组织架构管理的核心功能，用于建立和维护企业的部门层级结构。通过合理的部门设置，可以实现人员的有效分工、权限的精确控制和业务流程的规范化管理。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![部门管理主界面](../../../../assets/system/organizational/department-main.png) --> --> --> --> --> --> --> --> -->

## 功能特点

1. **层级化管理**：支持多级部门结构，灵活适应各种组织架构
2. **权限关联**：部门与员工权限紧密关联，实现精细化权限控制
3. **业务分工**：不同部门负责不同业务模块，职责清晰
4. **数据隔离**：基于部门的数据权限控制，确保数据安全
5. **灵活调整**：支持部门结构的动态调整和优化

## 重要提醒

:::caution 基础设置重要性
部门管理属于基础资料设置，是店铺新建后必须完善的基础信息。未完善部门设置的情况下，建议不要进行员工管理和权限分配等后续操作。
:::

## 访问路径

1. 在左侧导航菜单中，点击**系统设置**
2. 在展开的子菜单中，选择**组织架构**
3. 点击**部门管理**

## 部门架构规划

### 常见部门设置

在创建部门前，建议先规划好企业的组织架构：

#### 标准部门结构示例

```mermaid
graph TD
    A[总经理办公室] --> B[销售部]
    A --> C[采购部]
    A --> D[财务部]
    A --> E[仓储部]
    A --> F[客服部]

    B --> B1[销售一组]
    B --> B2[销售二组]

    C --> C1[采购一组]
    C --> C2[采购二组]

    D --> D1[会计组]
    D --> D2[出纳组]

    E --> E1[入库组]
    E --> E2[出库组]
```

#### 部门职责划分

| 部门名称 | 主要职责 | 相关功能模块 |
|----------|----------|--------------|
| 销售部 | 客户开发、订单处理、客户维护 | 客户管理、订单管理 |
| 采购部 | 供应商管理、采购计划、采购执行 | 采购管理、供应商管理 |
| 财务部 | 财务核算、资金管理、成本控制 | 财务管理、报表分析 |
| 仓储部 | 库存管理、出入库操作、盘点 | 库存管理、仓库管理 |
| 客服部 | 客户服务、售后处理、投诉处理 | 客户服务、订单跟踪 |

## 操作指南

### 创建部门

部门是企业中各个人员的归属单位，例如：采购员归属采购部，财务人员归属财务部等。

![部门新建位置](../../../../assets/system/organizational/department-create-location.png)

#### 创建主要部门

首先创建企业的主要部门，如：销售部、采购部、财务部等。

**操作步骤：**

1. **进入创建界面**
   - 在部门管理页面点击"新建部门"按钮
   - 页面右侧弹出部门创建表单

2. **填写部门信息**

   ![部门创建表单](../../../../assets/system/organizational/department-create-form.png)

   **必填字段：**
   - **部门名称**：输入部门的名称（如：财务部）
   - **部门编码**：系统自动生成或手动输入
   - **部门类型**：选择部门类型（主要部门/子部门）

   **可选字段：**
   - **上级部门**：新建主要部门时无需选择
   - **部门负责人**：选择部门负责人（可后续设置）
   - **部门描述**：添加部门的职责描述

3. **保存部门**
   - 检查信息无误后点击"确认"按钮
   - 部门创建成功后会显示在部门列表中

#### 创建子部门/小组

在主要部门下可以创建子部门或工作小组，实现更细致的组织管理。

**操作步骤：**

1. **选择创建类型**
   - 点击"新建小组"按钮
   - 或在主部门下点击"添加子部门"

2. **填写小组信息**

   <!-- ![小组创建表单](../../../../assets/system/organizational/department-group-create.png) -->

   **必填字段：**
   - **小组名称**：输入小组的名称（如：销售一组）
   - **上级部门**：选择所属的主要部门
   - **小组编码**：系统自动生成

   **可选字段：**
   - **小组负责人**：选择小组负责人
   - **职责范围**：描述小组的具体职责

3. **确认创建**
   - 点击"确认"按钮完成创建
   - 小组会显示在对应主部门下

### 编辑部门

对已创建的部门进行信息修改和调整。

#### 修改部门信息

1. **进入编辑模式**
   - 在部门列表中找到要编辑的部门
   - 点击部门行的"编辑"按钮

2. **修改部门信息**

   <!-- ![编辑部门界面](../../../../assets/system/organizational/department-edit.png) -->

   可修改的信息包括：
   - **部门名称**：修改部门名称
   - **部门负责人**：更换部门负责人
   - **上级部门**：调整部门层级关系
   - **部门描述**：更新部门职责描述
   - **部门状态**：启用或禁用部门

3. **保存修改**
   - 确认修改无误后点击"保存"
   - 修改会立即生效

#### 调整部门层级

1. **拖拽调整**
   - 在部门树形结构中拖拽部门节点
   - 调整部门的上下级关系

2. **批量调整**
   - 选择多个部门
   - 批量修改上级部门

### 删除部门

删除不再需要的部门或小组。

#### 删除前检查

系统会自动检查以下情况：
- **员工关联**：是否有员工归属于该部门
- **权限关联**：是否有权限角色关联该部门
- **业务数据**：是否有业务数据关联该部门

#### 删除操作

1. **选择删除对象**
   - 确保部门下没有员工
   - 点击部门行的"删除"按钮

2. **确认删除**
   - 系统弹出确认对话框
   - 仔细阅读删除影响提示
   - 点击"确定"执行删除

3. **处理关联数据**
   - 如有员工关联，需先转移员工
   - 如有权限关联，需先调整权限设置

## 部门权限管理

### 数据权限控制

部门设置直接影响员工的数据访问权限：

1. **本部门数据**：员工只能查看本部门的业务数据
2. **下级部门数据**：部门负责人可以查看下级部门数据
3. **跨部门协作**：通过权限设置实现跨部门数据共享

### 功能权限分配

不同部门的员工拥有不同的功能权限：

| 部门 | 主要权限 | 限制权限 |
|------|----------|----------|
| 销售部 | 客户管理、订单管理、价格查询 | 采购管理、财务管理 |
| 采购部 | 供应商管理、采购管理、库存查询 | 客户管理、财务管理 |
| 财务部 | 财务管理、报表查看、成本分析 | 商品管理、库存操作 |
| 仓储部 | 库存管理、出入库操作、盘点 | 客户管理、财务管理 |

## 最佳实践

### 部门设置建议

1. **层级不宜过深**
   - 建议不超过4级层级
   - 保持组织结构的简洁性

2. **职责清晰划分**
   - 每个部门的职责要明确
   - 避免职责重叠和空白

3. **灵活性考虑**
   - 预留组织调整的空间
   - 支持业务发展的需要

### 命名规范

1. **统一命名风格**
   - 使用统一的命名规则
   - 避免使用缩写和特殊字符

2. **层级标识**
   - 可以在名称中体现层级关系
   - 如：销售部-销售一组

3. **业务导向**
   - 部门名称要体现业务职能
   - 便于理解和记忆

## 注意事项

### 重要提醒

1. **基础设置优先**：部门管理是基础设置，应在员工管理之前完成
2. **谨慎删除**：删除部门会影响相关员工和权限设置
3. **权限影响**：部门调整会影响员工的数据访问权限
4. **业务连续性**：调整部门结构时要考虑业务的连续性

### 操作建议

1. **规划先行**：在创建部门前做好组织架构规划
2. **分步实施**：先创建主要部门，再逐步完善子部门
3. **定期维护**：定期检查和优化部门设置
4. **培训员工**：确保员工了解部门职责和权限范围

## 常见问题

### Q: 如何调整部门的上下级关系？
A: 可以通过以下方式调整：
- 编辑部门信息，修改上级部门
- 在部门树中拖拽调整位置
- 批量选择部门进行调整

### Q: 删除部门时提示有员工关联怎么办？
A: 需要先处理员工关联：
- 将员工转移到其他部门
- 或者先删除相关员工
- 确保部门下没有员工后再删除

### Q: 部门设置对权限有什么影响？
A: 部门设置直接影响：
- 员工的数据访问范围
- 功能模块的使用权限
- 业务流程的审批权限

### Q: 如何设置合理的部门层级？
A: 建议考虑：
- 企业规模和业务复杂度
- 管理跨度和效率
- 未来发展和扩展需要
- 一般不超过4级层级

## 相关功能

- [员工管理](./employee) - 管理部门下的员工
- [角色管理](./role) - 设置部门相关的权限角色
- [登录日志](./login-log) - 查看部门员工的登录记录
- [权限设置](../other/process-settings) - 配置部门相关的业务权限

