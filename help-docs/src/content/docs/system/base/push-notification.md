---
title: 消息推送
---

# 消息推送

## 功能介绍

消息推送模块用于管理系统中的各类通知消息，支持交易物流和营销关怀两大类消息的推送设置，帮助企业及时向用户传达重要信息。

## 主要功能

1. 交易物流通知：
   - 订单状态变更通知
   - 发货提醒
   - 收货提醒
   - 退款通知
   - 其他交易相关通知

2. 营销关怀通知：
   - 优惠活动提醒
   - 会员福利通知
   - 节日问候
   - 生日祝福
   - 其他营销消息

## 操作步骤

### 1. 开通消息推送

![消息推送设置页面](../../../../assets/system/base/push-notification/push-notification.png)

1. 点击右上角的【开通通知】按钮
2. 按照提示完成消息推送服务的开通流程
3. 开通成功后即可进行消息推送设置

### 2. 配置通知规则

1. 在列表页面可以查看所有已配置的通知规则
2. 通过顶部的分类标签切换不同类型的通知：
   - 全部：显示所有通知规则
   - 交易物流：仅显示交易相关通知
   - 营销关怀：仅显示营销相关通知

### 3. 管理通知模板

1. 选择需要配置的通知类型
2. 编辑通知内容和触发条件
3. 设置推送对象和推送时间
4. 保存设置使其生效

## 注意事项

1. 消息推送功能需要先开通才能使用
2. 不同类型的通知可能有不同的限制条件
3. 请遵守相关法律法规，不发送违规内容
4. 建议合理控制推送频率，避免打扰用户
5. 部分通知类型可能需要用户授权才能推送

## 常见问题

1. 无法收到推送消息：
   - 检查是否已正确开通推送服务
   - 确认用户是否允许接收推送
   - 验证推送规则是否正确配置

2. 推送延迟问题：
   - 检查网络连接状态
   - 确认服务器负载情况
   - 验证推送任务是否在队列中

3. 如何提高推送到达率：
   - 选择合适的推送时间
   - 优化推送内容的相关性
   - 避免频繁推送相似内容 