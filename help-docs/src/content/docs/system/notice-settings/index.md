---
title: 公告设置
description: 商城APP公告管理，包括公告的创建、编辑和发布管理
---

公告设置功能用于管理商城APP中的各类公告信息，帮助您及时向用户传达重要信息。

## 主要功能

### 公告管理
系统支持管理以下公告信息：
- 公告名称：公告的标题
- 公告详情：公告的具体内容
- 公告类型：不同类型的公告分类
- 状态：公告的显示状态
- 是否弹出：是否以弹窗形式展示

### 基础操作
- 新增公告：创建新的公告内容
- 编辑：修改现有公告内容
- 删除：移除不需要的公告
- 发布/撤回：控制公告的显示状态

### 分页功能
- 每页显示数量可调整（默认10条/页）
- 支持页码跳转
- 显示总记录数

## 操作步骤

1. 点击"新增公告"按钮创建新公告
2. 填写公告基本信息：
   - 公告名称
   - 公告详情
   - 选择公告类型
   - 设置是否弹出显示
3. 设置公告状态（启用/禁用）
4. 保存公告信息

## 界面预览

![公告设置界面](../../../../assets/system/shop-decoration/notice-settings.png)

## 注意事项

- 建议定期检查并更新过期公告
- 重要公告可设置为弹窗显示，提高用户关注度
- 公告内容应简明扼要，便于用户阅读
- 可以通过状态控制公告的显示时机
- 删除操作不可恢复，请谨慎操作