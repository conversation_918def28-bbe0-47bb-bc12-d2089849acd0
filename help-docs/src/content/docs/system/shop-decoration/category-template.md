---
title: 分类模板
description: 商城APP分类页面展示模板设置
---

分类模板用于设置商城APP中商品分类页面的展示样式，帮助用户更好地浏览和查找商品。

## 主要功能

### 分类样式选择
系统提供以下几种分类展示样式：
- 一级分类（大图）：以大图形式展示一级分类
- 一级分类（小图）：以小图标形式展示一级分类
- 二级分类：展示一级分类及其下属二级分类
- 三级分类：完整展示三级分类结构
- 分类+商品：在分类的同时展示推荐商品

### 页面布局预览
- 搜索框位置
- 热门推荐区域
- 分类列表展示
- 底部导航栏

## 操作步骤

1. 进入分类模板设置页面
2. 在右侧选择合适的分类展示样式
3. 在左侧预览区域查看效果
4. 确认无误后点击"提交"按钮保存设置

## 界面预览

![分类模板设置界面](../../../../assets/system/shop-decoration/category-template.png)

## 注意事项

- 分类样式变更后会即时在预览区域显示效果
- 建议根据商品种类数量选择合适的分类层级
- 可以随时切换不同样式进行对比
- 设置保存后会立即在APP端生效