---
title: 启动页设置
---

# 启动页设置

启动页是用户打开小程序时首先看到的页面，可以用来展示品牌形象或重要信息。

## 功能位置

系统设置 > 店铺装修 > 启动页

## 主要功能

### 1. 启动页开关设置

- 可以选择启用或禁用启动页功能
- 启用后，用户打开小程序时会先显示启动页

### 2. 定时关闭设置

- 可以设置启动页显示时长
- 提供3秒、4秒、5秒三个选项
- 时间到后自动跳转到目标页面

### 3. 启动页图片设置

- 可以上传启动页图片
- 建议图片尺寸：750px × 1334px
- 支持图片预览功能

### 4. 链接设置

- 可以设置启动页跳转的目标页面
- 输入页面路径，如：/pages/index/index

## 操作指南

1. 进入启动页设置页面
2. 选择是否启用启动页
3. 设置启动页显示时长
4. 上传启动页图片
5. 设置跳转链接
6. 点击"提交保存"按钮保存设置

![启动页设置](../../../../assets/shop-decoration/startup-page.png)

## 注意事项

1. 启动页图片需要清晰美观，建议使用专业设计的图片
2. 显示时间不宜过长，以免影响用户体验
3. 确保跳转链接正确，避免出现404错误