---
title: 客户类型
description: 客户类型功能介绍与操作指南
---

# 客户类型

客户类型是对客户进行分类管理的基础功能，通过设置不同的客户类型，可以更有效地管理和区分不同性质的客户群体。

## 页面概览

客户类型页面主要展示了系统中已有的客户类型列表，包括类型名称、是否默认、状态等信息。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![客户类型主页](../../../../assets/customer-type/customer-type-main.png) --> --> --> --> --> --> --> --> --> -->

## 功能说明

### 1. 列表展示

客户类型列表显示了以下信息：
- ID：客户类型的唯一标识
- 客户类型：类型名称
- 是否默认：标识该类型是否为系统默认类型
- 状态：显示该类型是否启用
- 操作：可执行的操作按钮

### 2. 客户类型管理

#### 2.1 新增客户类型

系统支持创建新的客户类型来满足业务需求。

操作步骤：
1. 点击页面右上角的"新增类型"按钮
2. 在弹出的表单中填写类型名称
3. 设置是否为默认类型
4. 点击保存完成创建

> 注意：系统提示"系统默认客户类型不允许操作"表明有些预设类型不可修改

#### 2.2 编辑客户类型

您可以修改非系统默认的客户类型信息。

操作步骤：
1. 在客户类型列表中找到需要编辑的类型
2. 点击操作列中的"编辑"按钮
3. 在弹出的表单中修改相关信息
4. 点击保存完成修改

#### 2.3 设为默认类型

将特定客户类型设置为系统默认类型。

操作步骤：
1. 在客户类型列表中找到需要设置的类型
2. 点击操作列中的"设为默认"按钮
3. 确认操作后该类型将被标记为默认类型

#### 2.4 删除客户类型

删除不再使用的客户类型。

操作步骤：
1. 在客户类型列表中找到需要删除的类型
2. 点击操作列中的"删除"按钮
3. 在弹出的确认框中点击确认完成删除

> 注意：系统默认类型不允许删除，且已关联客户的类型删除后可能影响现有客户数据

### 3. 导出功能

系统支持将客户类型数据导出为Excel或其他格式文件。

操作步骤：
1. 点击页面右上角的"导出"按钮
2. 选择导出格式
3. 等待系统生成文件并自动下载

## 注意事项

1. 系统默认的客户类型不允许修改或删除
2. 创建新类型时，名称不能与现有类型重复
3. 删除客户类型会影响已关联该类型的客户资料
4. 每个客户必须关联一个客户类型，建议在删除类型前先修改关联客户的类型 