---
title: 客户列表管理
description: 学习如何管理客户列表，包括客户查询、筛选、新增、编辑等全面的客户管理功能
tags: [customer, management, list, search, filter, admin]
keywords: [客户列表, 客户管理, 客户查询, 客户筛选, CRM]
---

# 客户列表管理

客户列表是升辉ERP系统中客户关系管理(CRM)的核心功能，用于集中管理所有客户信息。通过客户列表，您可以全面了解客户基本信息、交易历史、信用状况等，为精准营销和客户服务提供数据支撑。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![客户列表主界面](../../../../assets/customer/customer-list-main.png) --> --> --> --> --> --> --> --> --> -->

## 功能特点

1. **全面的客户信息**：展示客户的完整档案信息，包括基本信息、联系方式、地址等
2. **多维度筛选**：支持按区域、类型、状态、业务员等多种条件筛选客户
3. **快速搜索**：支持按客户名称、电话、编号等关键字快速搜索
4. **批量操作**：支持批量启用、禁用、导出等操作，提高管理效率
5. **权限控制**：精细的数据权限控制，确保数据安全
6. **实时统计**：显示客户总数、活跃客户数等统计信息

## 访问路径

1. 在左侧导航菜单中，点击**客户管理**
2. 在展开的子菜单中，选择**客户列表**

## 页面布局说明

### 主要功能区域

#### 1. 搜索筛选区域
- **快速搜索框**：支持按客户名称、电话、编号搜索
- **高级筛选**：提供多种筛选条件组合
- **重置按钮**：一键清除所有筛选条件

#### 2. 操作工具栏
- **新增客户**：创建新的客户档案
- **批量操作**：对选中客户执行批量操作
- **导出功能**：导出客户数据

#### 3. 客户列表表格
显示所有客户的详细信息：

| 列名 | 说明 | 备注 |
|------|------|------|
| ID | 客户的唯一标识符 | 系统自动生成 |
| 客户姓名 | 客户的姓名或公司名称 | 支持点击查看详情 |
| 登录账号 | 客户的登录账号 | 通常为手机号 |
| 联系电话 | 客户的联系电话 | 支持点击拨打 |
| 所在区域 | 客户的地理位置 | 省市区信息 |
| 客户类型 | 客户所属的分类 | 如批发、零售等 |
| 客户状态 | 客户的当前状态 | 启用、禁用、待审核等 |
| 注册时间 | 客户的注册时间 | 便于了解客户历史 |
| 最后登录 | 客户最后一次登录时间 | 反映客户活跃度 |
| 操作 | 可执行的操作按钮 | 查看、编辑、更多操作 |

## 操作指南

### 客户搜索功能

#### 快速搜索

系统提供强大的搜索功能，支持多种搜索方式：

1. **按客户名称搜索**
   - 在搜索框中输入客户姓名或公司名称
   - 支持模糊搜索，输入部分名称即可匹配

2. **按联系方式搜索**
   - 输入手机号码或固定电话
   - 支持完整号码或部分号码搜索

3. **按客户编号搜索**
   - 输入系统生成的客户编号
   - 精确匹配，快速定位特定客户

<!-- <!-- <!-- ![客户搜索界面](../../../../assets/customer-list/customer-list-search.png) --> --> -->

**搜索操作步骤：**
1. 在页面顶部搜索框中输入关键词
2. 点击搜索按钮或按回车键
3. 系统显示匹配的客户列表
4. 点击"清除"按钮可清空搜索条件

### 高级筛选功能

系统提供多维度筛选功能，帮助您精确查找目标客户：

#### 按业务员筛选

查看特定业务员负责的客户：

1. 点击"业务员"下拉框
2. 选择目标业务员
3. 系统显示该业务员的客户列表
4. 便于业务员管理自己的客户资源

#### 按区域筛选

支持三级区域筛选（省-市-区）：

<!-- ![区域筛选界面](../../../../assets/customer-list/customer-list-region-filter.png) -->

1. **省份筛选**
   - 选择目标省份
   - 查看该省份的所有客户

2. **城市筛选**
   - 在选定省份基础上选择城市
   - 进一步缩小筛选范围

3. **区县筛选**
   - 精确到区县级别
   - 实现最精准的地域筛选

#### 按客户分类筛选

根据业务需要对客户进行分类筛选：

<!-- <!-- <!-- ![分类筛选界面](../../../../assets/customer-list/customer-list-category-filter.png) --> --> -->

**常见客户分类：**
- **批发客户**：大宗采购的批发商
- **零售客户**：终端消费者
- **代理商**：区域代理合作伙伴
- **经销商**：产品经销商
- **VIP客户**：重要客户

#### 按状态筛选

根据客户状态进行筛选管理：

<!-- <!-- ![状态筛选界面](../../../../assets/customer-list/customer-list-status-filter.png) --> -->

**客户状态说明：**

| 状态 | 说明 | 特点 |
|------|------|------|
| 已启用 | 正常活跃的客户 | 可以正常下单和交易 |
| 已禁用 | 被暂停服务的客户 | 无法登录和下单 |
| 待审核 | 新注册等待审核的客户 | 需要人工审核后才能使用 |
| 审核拒绝 | 审核未通过的客户 | 需要重新提交资料 |

### 客户管理操作

#### 新增客户

为系统添加新的客户档案：

<!-- ![新增客户按钮](../../../../assets/customer-list/customer-list-add-button.png) -->

1. **点击新增按钮**
   - 在页面右上角点击"新增客户"按钮
   - 系统弹出客户信息录入表单

2. **填写基本信息**

   **必填字段：**
   - **客户名称**：客户的姓名或公司名称
   - **登录账号**：通常使用手机号作为登录账号
   - **联系电话**：客户的主要联系电话
   - **客户类型**：选择客户所属的分类
   - **所在区域**：选择客户的地理位置

   **可选字段：**
   - **客户标签**：为客户添加标签便于管理
   - **联系地址**：客户的详细地址信息
   - **备注信息**：其他需要记录的信息

3. **保存客户信息**
   - 检查信息无误后点击"保存"按钮
   - 系统自动为客户分配唯一编号
   - 后台手动新增的客户自动通过审核

#### 查看客户详情

深入了解客户的完整信息：

1. **进入详情页面**
   - 点击客户姓名或"查看"按钮
   - 系统打开客户详情页面

2. **详情页面内容**
   - **基本信息**：客户的基础档案信息
   - **联系信息**：电话、地址等联系方式
   - **交易记录**：历史订单和交易情况
   - **信用信息**：信用额度、欠款情况等
   - **操作记录**：客户档案的修改历史

#### 编辑客户信息

修改客户的档案信息：

1. **编辑权限检查**
   - 已审核客户需要先禁用才能编辑
   - 确保有相应的编辑权限

2. **修改客户信息**
   - 点击"编辑"按钮进入编辑模式
   - 修改需要更新的字段信息
   - 保存修改后的信息

3. **编辑注意事项**
   - 关键信息修改可能需要重新审核
   - 修改后的信息会立即生效
   - 系统会记录修改历史

#### 批量操作

提高客户管理效率的批量操作功能：

1. **选择客户**
   - 勾选需要操作的客户复选框
   - 可使用全选功能选择当前页面所有客户

2. **执行批量操作**
   - **批量启用**：批量启用被禁用的客户
   - **批量禁用**：批量禁用客户账户
   - **批量导出**：导出选中客户的详细信息
   - **批量分配**：批量分配业务员

#### 更多操作

通过"更多"下拉菜单执行其他操作：

- **重置密码**：为客户重置登录密码
- **发送通知**：向客户发送系统通知
- **查看订单**：查看客户的历史订单
- **信用管理**：设置客户的信用额度
- **删除客户**：删除客户档案（谨慎操作）

## 客户状态管理

### 状态流转规则

客户状态按照以下规则进行流转：

```mermaid
graph TD
    A[新注册] --> B[待审核]
    B --> C{审核结果}
    C -->|通过| D[已启用]
    C -->|拒绝| E[审核拒绝]
    D --> F[已禁用]
    F --> D
    E --> B
```

### 状态操作权限

| 状态变更 | 所需权限 | 说明 |
|----------|----------|------|
| 审核客户 | 客户管理-审核 | 可以审核待审核客户 |
| 启用客户 | 客户管理-启用 | 可以启用被禁用的客户 |
| 禁用客户 | 客户管理-禁用 | 可以禁用客户账户 |
| 删除客户 | 客户管理-删除 | 可以删除客户档案 |

## 权限说明

| 操作 | 所需权限 | 说明 |
|------|----------|------|
| 查看客户列表 | 客户管理-查看 | 可以查看客户列表和基本信息 |
| 新增客户 | 客户管理-新增 | 可以创建新客户档案 |
| 编辑客户 | 客户管理-编辑 | 可以修改客户信息 |
| 删除客户 | 客户管理-删除 | 可以删除客户档案 |
| 批量操作 | 客户管理-批量操作 | 可以执行批量操作 |
| 导出数据 | 客户管理-导出 | 可以导出客户数据 |

## 注意事项

### 重要提醒

1. **编辑限制**：已审核客户只有在禁用后才能编辑，这是为了保证数据的稳定性
2. **自动审核**：后台手动新增的客户自动通过审核，无需额外审核流程
3. **数据安全**：客户信息涉及隐私，请严格按照权限要求操作
4. **删除谨慎**：删除客户会影响相关的订单和交易记录，请谨慎操作

### 操作建议

1. **定期维护**：定期检查和更新客户信息，保持数据的准确性
2. **分类管理**：合理使用客户分类和标签，便于精准营销
3. **权限控制**：严格控制客户管理权限，确保数据安全
4. **备份重要数据**：定期导出重要客户数据进行备份

## 常见问题

### Q: 为什么无法编辑某个客户的信息？
A: 可能的原因：
- 客户状态为"已启用"，需要先禁用才能编辑
- 没有相应的编辑权限
- 客户正在被其他用户编辑

### Q: 如何批量导入客户信息？
A: 目前系统支持手动新增和批量操作，如需批量导入，请联系系统管理员。

### Q: 客户状态为什么会自动变化？
A: 客户状态可能因为以下原因自动变化：
- 系统自动审核规则触发
- 客户长期未登录被自动禁用
- 信用额度超限被自动禁用

### Q: 如何恢复误删的客户？
A: 客户删除操作不可恢复，建议：
- 删除前仔细确认
- 对重要客户使用禁用而不是删除
- 定期备份客户数据

## 相关功能

### 客户管理相关
- [客户类型管理](./customer-type) - 管理客户分类体系
- [标签管理](./label-management) - 管理客户标签系统
- [客户审核](../check/unaudited) - 审核新注册客户
- [客户行为分析](../behavior/browsing-history) - 分析客户行为数据

### 业务流程相关
- [订单管理](../../order/manage/order-list) - 查看客户订单记录
- [财务往来](../../finance/section1/customer-summary) - 管理客户财务关系
- [客户报表](../statement/abnormal-analysis) - 查看客户分析报表

### 营销推广相关
- [营销活动](../../marketing/bill-template/bill-template) - 针对客户的营销推广
- [会员管理](../../other/member-management) - 客户会员体系管理