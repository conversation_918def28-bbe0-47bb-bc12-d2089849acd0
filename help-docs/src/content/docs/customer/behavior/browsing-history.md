---
title: 浏览记录
description: 客户浏览商品的历史记录查询与管理
---

# 浏览记录

浏览记录功能用于查看和分析客户对商品的浏览行为，帮助商家了解客户的兴趣偏好，优化商品推荐和营销策略。

## 功能概述

浏览记录页面提供以下核心功能：

- 查看客户浏览过的商品记录
- 通过客户名称、联系方式筛选浏览记录
- 根据商品分类筛选浏览记录
- 设置浏览时间范围进行筛选
- 分页查看大量浏览记录数据

<!-- <!-- ![浏览记录主界面](../../../../assets/customer-behavior/browsing-history/main-view.png) --> -->

## 操作指南

### 查询浏览记录

1. 在左侧导航栏中，选择"客户" > "客户行为" > "浏览记录"

2. 系统默认展示所有客户的浏览记录，按浏览时间降序排列

### 客户筛选

您可以通过客户信息筛选特定客户的浏览记录：

1. 在页面上方的"请选择客户"搜索框中输入客户名称或联系方式
2. 系统会自动匹配符合条件的客户
3. 选择目标客户后，系统将只显示该客户的浏览记录

<!-- ![客户搜索筛选](../../../../assets/customer-behavior/browsing-history/search-customer.png) -->

### 商品分类筛选

您可以根据商品分类筛选浏览记录：

1. 点击"请选择商品分类"下拉框
2. 在弹出的分类列表中选择需要的商品分类
3. 系统将只显示该分类下商品的浏览记录

<!-- <!-- ![商品分类筛选](../../../../assets/customer-behavior/browsing-history/category-filter.png) --> -->

### 日期范围筛选

您可以设置特定的日期范围，查看特定时间段内的浏览记录：

1. 点击日期选择框
2. 在日历控件中选择开始日期和结束日期
3. 系统将只显示选定日期范围内的浏览记录

<!-- ![日期范围筛选](../../../../assets/customer-behavior/browsing-history/date-filter.png) -->

### 分页查看

当浏览记录数量较多时，系统会自动分页显示：

1. 在页面底部可以看到总记录数和分页控制栏
2. 可以点击页码直接跳转到指定页
3. 可以使用"上一页"和"下一页"按钮浏览相邻页面
4. 可以在页码输入框中直接输入页码并按回车跳转

<!-- <!-- <!-- ![分页功能](../../../../assets/customer-behavior/browsing-history/pagination.png) --> --> -->

### 重置筛选条件

如果需要清除所有筛选条件，回到初始状态：

1. 点击页面右上角的"重置筛选"按钮
2. 系统将清除所有筛选条件，显示全部浏览记录

## 数据说明

浏览记录表格包含以下字段：

| 字段名称 | 说明 |
|---------|------|
| 商品名称 | 客户浏览的商品名称 |
| 客户名称 | 浏览商品的客户名称 |
| 客户电话 | 客户的联系电话 |
| 浏览时间 | 客户浏览该商品的具体时间 |

## 应用场景

浏览记录功能适用于以下业务场景：

1. **客户兴趣分析**：了解客户关注的商品类型和兴趣偏好
2. **营销策略制定**：根据浏览行为设计个性化的营销活动
3. **商品热度评估**：分析被浏览次数较多的商品，了解市场热点
4. **客户行为跟踪**：结合其他行为数据，全面了解客户行为轨迹

## 常见问题

**Q: 浏览记录保存多长时间?**  
A: 系统默认保存最近3个月的浏览记录数据。

**Q: 如何导出浏览记录数据?**  
A: 当前版本暂不支持导出功能，后续版本将增加此功能。

**Q: 为什么有些客户没有浏览记录?**  
A: 只有通过系统浏览过商品的客户才会产生浏览记录。如果客户未登录或使用其他渠道，可能不会记录浏览行为。 