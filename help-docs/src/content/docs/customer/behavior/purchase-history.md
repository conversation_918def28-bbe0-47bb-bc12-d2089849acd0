---
title: 购买记录
description: 客户购买商品的历史记录查询与管理
---

# 购买记录

购买记录功能用于查看和分析客户的商品购买行为，帮助商家了解客户的消费习惯，优化库存管理和销售策略。

## 功能概述

购买记录页面提供以下核心功能：

- 查看客户购买过的商品详细记录
- 通过客户名称、联系方式筛选购买记录
- 通过商品名称筛选购买记录
- 根据商品分类筛选购买记录
- 设置购买时间范围进行筛选
- 分页查看大量购买记录数据
- 导出购买记录数据

<!-- <!-- ![购买记录主界面](../../../../assets/customer-behavior/purchase-history/main-view.png) --> -->

## 操作指南

### 查询购买记录

1. 在左侧导航栏中，选择"客户" > "客户行为" > "购买记录"

2. 系统默认展示所有客户的购买记录，按购买时间降序排列

### 客户筛选

您可以通过客户信息筛选特定客户的购买记录：

1. 在页面上方的"请选择客户"搜索框中输入客户名称或联系方式
2. 系统会自动匹配符合条件的客户
3. 选择目标客户后，系统将只显示该客户的购买记录

<!-- <!-- <!-- ![客户搜索筛选](../../../../assets/customer-behavior/purchase-history/customer-search.png) --> --> -->

筛选后的结果：

<!-- ![客户筛选结果](../../../../assets/customer-behavior/purchase-history/filtered-results.png) -->

### 商品名称筛选

您可以根据商品名称筛选购买记录：

1. 在"商品名称"搜索框中输入商品名称关键词
2. 按下回车键或点击搜索图标
3. 系统将只显示包含该关键词的商品购买记录

<!-- <!-- <!-- ![商品名称筛选](../../../../assets/customer-behavior/purchase-history/product-search.png) --> --> -->

### 商品分类筛选

您可以根据商品分类筛选购买记录：

1. 点击"请选择商品分类"下拉框
2. 在弹出的分类列表中选择需要的商品分类
3. 系统将只显示该分类下商品的购买记录

<!-- ![商品分类筛选](../../../../assets/customer-behavior/purchase-history/category-dropdown.png) -->

选择分类后的结果：

<!-- ![分类筛选结果](../../../../assets/customer-behavior/purchase-history/category-filtered.png) -->

### 日期范围筛选

您可以设置特定的日期范围，查看特定时间段内的购买记录：

1. 点击日期选择框
2. 在日历控件中选择开始日期和结束日期
3. 系统将只显示选定日期范围内的购买记录

### 分页查看

当购买记录数量较多时，系统会自动分页显示：

1. 在页面底部可以看到总记录数和分页控制栏
2. 可以点击页码直接跳转到指定页
3. 可以使用"上一页"和"下一页"按钮浏览相邻页面
4. 可以在页码输入框中直接输入页码并按回车跳转

<!-- <!-- <!-- ![分页功能](../../../../assets/customer-behavior/purchase-history/pagination.png) --> --> -->

### 重置筛选条件

如果需要清除所有筛选条件，回到初始状态：

1. 点击页面右上角的"重置筛选"按钮
2. 系统将清除所有筛选条件，显示全部购买记录

<!-- <!-- <!-- ![重置筛选](../../../../assets/customer-behavior/purchase-history/reset-filter.png) --> --> -->

### 导出数据

您可以将当前筛选条件下的购买记录导出为Excel文件：

1. 设置好所需的筛选条件
2. 点击页面右上角的"导出"按钮
3. 系统将自动生成Excel文件并下载到本地

<!-- <!-- ![导出功能](../../../../assets/customer-behavior/purchase-history/export-button.png) --> -->

## 数据说明

购买记录表格包含以下字段：

| 字段名称 | 说明 |
|---------|------|
| 客户 | 购买商品的客户名称 |
| 商品 | 客户购买的商品名称 |
| 单位 | 商品的计量单位 |
| 属性 | 商品的具体规格属性 |
| 单价 | 商品的销售单价 |
| 购买数量 | 客户购买的商品数量 |
| 商品总价 | 该笔购买的商品总价（单价×数量） |
| 发货数量 | 已发货的商品数量 |
| 商品条码 | 商品的唯一识别编码 |

## 应用场景

购买记录功能适用于以下业务场景：

1. **客户消费分析**：了解客户的购买习惯和偏好
2. **销售策略制定**：根据购买行为设计个性化的营销活动
3. **库存管理优化**：分析热销商品，合理调整库存
4. **客户关系维护**：了解客户购买历史，提供更好的售后服务

## 常见问题

**Q: 购买记录保存多长时间?**  
A: 系统默认保存所有历史购买记录，无时间限制。

**Q: 如何查看某个订单的详细信息?**  
A: 当前版本需要通过订单管理模块查看订单详情，后续版本将增加直接跳转功能。

**Q: 为什么有些购买记录没有发货数量?**  
A: 发货数量为0表示订单已创建但尚未发货，可以在订单管理中查看详细状态。 