---
title: 异常客户分析表
description: 异常客户分析表的使用指南与操作说明
--- 

# 异常客户分析表

异常客户分析表是一个用于监控和分析客户异常行为的重要工具。通过此表，您可以快速识别长期未下单、未拜访或存在其他异常情况的客户，帮助业务人员及时跟进，提高客户留存率。

## 功能概述

异常客户分析表主要提供以下功能：

- **异常分组**：将客户按照异常类型进行分组展示
- **多维度筛选**：支持按客户名称、业务员和地区进行筛选
- **订单数据展示**：显示客户本年订单金额、本月订单金额、上月订单金额等数据
- **拜访情况展示**：展示本年拜访次数、本月拜访次数、上月拜访次数等数据
- **注册天数**：显示客户注册的天数
- **距上次拜访天数**：展示距离上次拜访的天数

## 操作指南

### 1. 进入异常客户分析表

在系统左侧导航栏选择【客户】->【报表】->【异常客户分析表】进入页面。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![异常客户分析表主界面](../../../../assets/customer-statements/abnormal-analysis/main.png) --> --> --> --> --> --> --> --> -->

### 2. 筛选功能

#### 2.1 按客户名称筛选

在顶部的"客户名称"搜索框中输入客户名称关键词，系统会自动搜索匹配的客户。

<!-- <!-- ![按客户名称筛选](../../../../assets/customer-statements/abnormal-analysis/search.png) --> -->

点击搜索图标或按回车键后，系统将显示筛选结果：

<!-- <!-- ![客户名称筛选结果](../../../../assets/customer-statements/abnormal-analysis/search_result.png) --> -->

#### 2.2 按业务员筛选

在"业务员"搜索框中输入业务员姓名或点击搜索图标，系统会弹出业务员选择列表：

<!-- ![业务员选择](../../../../assets/customer-statements/abnormal-analysis/employee_selection.png) -->

在列表中选择相应的业务员后，系统会自动应用筛选：

<!-- ![业务员筛选结果](../../../../assets/customer-statements/abnormal-analysis/filter_result.png) -->

#### 2.3 按地区筛选

点击"地区"下拉框，系统会显示可选择的地区列表：

<!-- ![地区筛选下拉框](../../../../assets/customer-statements/abnormal-analysis/region_dropdown.png) -->

选择需要的地区后，系统会应用地区筛选。

### 3. 重置筛选

点击右上角的"重置筛选"按钮，可以清除所有筛选条件，恢复到初始状态：

<!-- <!-- ![重置筛选](../../../../assets/customer-statements/abnormal-analysis/reset_filters.png) --> -->

## 数据解读

列表中各列数据的含义如下：

- **异常分组**：客户所属的异常类型，例如"久未订货"、"新注册未下单"等
- **客户名称**：客户的名称
- **客户编码**：系统分配的客户唯一标识
- **客户地区**：客户所在的地区
- **客户类型**：客户的类型分类
- **业务员**：负责该客户的业务员
- **注册天数**：客户在系统中注册的天数
- **本年订单金额**：本年度累计订单金额
- **本月订单金额**：本月累计订单金额
- **上月订单金额**：上月累计订单金额
- **本年订单个数**：本年度累计订单数量
- **本月订单个数**：本月累计订单数量
- **上月订单个数**：上月累计订单数量
- **本年拜访次数**：本年度累计拜访次数
- **距上次拜访多少天**：距离上次拜访的天数

## 注意事项

1. 异常客户分析表默认展示所有异常客户，建议配合筛选功能缩小查询范围
2. 可以根据"距上次拜访多少天"列识别长期未跟进的客户
3. "本月订单个数"和"本年拜访次数"为0的客户需要特别关注
4. 系统数据每日更新，建议定期查看异常客户状况 