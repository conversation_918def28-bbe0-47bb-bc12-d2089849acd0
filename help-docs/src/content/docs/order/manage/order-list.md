---
title: 订货单管理
description: 学习如何管理订货单，包括查看、处理、发货、退款等订单全生命周期操作
---

# 订货单管理

订货单管理是ERP系统的核心功能之一，用于处理客户的购买订单。通过订货单管理，您可以查看订单详情、处理订单状态、安排发货、处理退款等，确保订单流程的顺畅进行。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![订货单列表主界面](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/order-list-main.png) --> --> --> --> --> --> --> --> -->

订货单管理页面提供了完整的订单管理功能，主要包含以下区域：

**温馨提示区域**：
- 双击列表可查看对应订单详情
- 订单审核后，会自动生成出库单
- 待审核订单可以进行编辑金额操作，审核后无法编辑

**筛选条件区域**：
- 单据编号搜索
- 订单状态筛选（待审核、已审核等）
- 退货状态筛选
- 客户选择
- 下单时间范围选择
- 所属店铺筛选
- 支付方式筛选
- 客户类型筛选
- 订单来源筛选
- 出库状态筛选
- 支付状态筛选

**订单列表区域**：
显示订单的详细信息，包括客户、订单金额、商品、订单状态、付款状态、退货状态、审核状态、配送方式、支付方式、业务员、订单来源、创建时间、商铺等信息。

## 功能特点

1. **全面的订单信息**：显示订单的完整信息，包括客户、商品、金额、状态等
2. **多维度筛选**：支持按订单状态、时间、客户、商品等多种条件筛选
3. **批量操作**：支持批量发货、批量导出等操作，提高处理效率
4. **状态跟踪**：实时跟踪订单状态变化，支持订单全生命周期管理
5. **灵活处理**：支持订单修改、拆分、合并等灵活处理方式

## 访问路径

1. 在左侧导航菜单中，点击**订单管理**
2. 在展开的子菜单中，选择**订货单**

## 订单状态说明

订单在系统中有以下几种状态：

| 状态 | 说明 | 可执行操作 |
|------|------|-----------|
| 待确认 | 客户刚提交的订单，等待商家确认 | 确认订单、取消订单 |
| 待发货 | 已确认的订单，等待发货 | 发货、修改订单 |
| 已发货 | 订单已发货，等待客户收货 | 查看物流、催收货 |
| 已完成 | 客户已确认收货，订单完成 | 查看详情、申请售后 |
| 已取消 | 订单已取消 | 查看详情 |
| 退款中 | 订单正在退款处理中 | 处理退款 |
| 已退款 | 订单退款已完成 | 查看详情 |

## 操作指南

### 查看订单列表

![订单筛选区域](/assets/images/order/manage/order-filter-area.png)

订单列表支持多种筛选条件：

**时间筛选**
- 今天、昨天、本周、本月等快捷时间选择
- 自定义时间范围选择

**状态筛选**
- 全部订单
- 待确认订单
- 待发货订单
- 已发货订单
- 已完成订单
- 已取消订单

**其他筛选**
- 客户名称或手机号搜索
- 订单编号搜索
- 商品名称搜索
- 配送方式筛选

### 处理订单

#### 确认订单

对于待确认状态的订单：

1. 在订单列表中找到待确认的订单
2. 点击**确认订单**按钮
3. 检查订单信息是否正确
4. 确认后订单状态变为"待发货"

![确认订单界面](/assets/images/order/manage/confirm-order.png)

#### 订单发货

对于待发货状态的订单：

1. 点击订单操作列的**发货**按钮
2. 在发货表单中填写以下信息：

![订单发货表单](/assets/images/order/manage/ship-order-form.png)

**发货信息字段：**

| 字段名称 | 是否必填 | 说明 |
|----------|----------|------|
| 物流公司 | 是 | 选择承运的物流公司 |
| 运单号 | 是 | 填写物流运单号 |
| 发货时间 | 是 | 选择实际发货时间 |
| 发货备注 | 否 | 填写发货相关备注信息 |

3. 填写完成后点击**确认发货**
4. 系统自动发送发货通知给客户

#### 查看订单详情

点击订单编号或**详情**按钮可查看订单完整信息：

<!-- ![订单详情页面](/assets/images/order/manage/order-detail.png) -->

订单详情包含：
- **基本信息**：订单编号、下单时间、订单状态等
- **客户信息**：客户姓名、联系方式、收货地址等
- **商品信息**：商品列表、数量、价格等
- **金额信息**：商品金额、运费、优惠金额、实付金额等
- **物流信息**：发货时间、物流公司、运单号等
- **操作记录**：订单的所有操作历史记录

### 批量操作

选择多个订单后可执行批量操作：

![批量操作界面](/assets/images/order/manage/batch-operations.png)

**支持的批量操作：**
- **批量发货**：对多个待发货订单批量处理发货
- **批量导出**：导出选中订单的详细信息
- **批量打印**：批量打印订单发货单或配货单
- **批量修改状态**：批量修改订单状态

### 订单修改

对于待发货状态的订单，支持修改订单信息：

1. 点击订单操作列的**修改**按钮
2. 在修改表单中调整订单信息：
   - 修改商品数量
   - 修改收货地址
   - 修改配送方式
   - 添加或删除商品
3. 点击**保存修改**完成操作

![修改订单界面](/assets/images/order/manage/edit-order.png)

### 订单取消

对于待确认或待发货状态的订单可以取消：

1. 点击订单操作列的**取消**按钮
2. 选择取消原因：
   - 客户要求取消
   - 商品缺货
   - 价格错误
   - 其他原因
3. 填写取消备注
4. 点击**确认取消**

![取消订单界面](/assets/images/order/manage/cancel-order.png)

### 退款处理

对于需要退款的订单：

1. 点击订单操作列的**退款**按钮
2. 填写退款信息：

![退款处理界面](/assets/images/order/manage/refund-order.png)

**退款信息字段：**

| 字段名称 | 是否必填 | 说明 |
|----------|----------|------|
| 退款金额 | 是 | 填写实际退款金额 |
| 退款原因 | 是 | 选择退款原因 |
| 退款方式 | 是 | 选择退款方式（原路返回、银行转账等） |
| 退款备注 | 否 | 填写退款相关说明 |

3. 点击**确认退款**提交退款申请
4. 财务审核通过后执行退款

## 权限说明

| 操作 | 所需权限 | 说明 |
|------|----------|------|
| 查看订单 | 订单管理-查看 | 可以查看订单列表和详情 |
| 确认订单 | 订单管理-确认 | 可以确认待处理订单 |
| 订单发货 | 订单管理-发货 | 可以处理订单发货 |
| 修改订单 | 订单管理-编辑 | 可以修改订单信息 |
| 取消订单 | 订单管理-取消 | 可以取消订单 |
| 退款处理 | 订单管理-退款 | 可以处理订单退款 |
| 批量操作 | 订单管理-批量操作 | 可以执行批量操作 |

## 注意事项

1. **状态流转**：订单状态按照固定流程流转，不可逆向操作
2. **库存影响**：确认订单时会占用库存，取消订单时会释放库存
3. **发货信息**：发货时必须填写准确的物流信息，便于客户跟踪
4. **退款审核**：退款操作需要相应权限，大额退款可能需要额外审批
5. **数据备份**：重要操作前建议备份订单数据

## 常见问题

**Q: 订单确认后发现信息错误怎么办？**
A: 如果订单还未发货，可以使用订单修改功能调整信息。如果已发货，需要联系客户协商处理。

**Q: 客户要求修改收货地址怎么处理？**
A: 如果订单未发货，可以直接修改订单中的收货地址。如果已发货，需要联系物流公司修改配送地址。

**Q: 批量发货时部分订单失败怎么办？**
A: 系统会显示具体的失败原因，可以针对失败的订单单独处理，或修正问题后重新批量操作。

**Q: 如何处理客户投诉订单问题？**
A: 可以在订单详情页面查看完整的操作记录，了解订单处理过程，并根据具体情况进行相应处理。

**Q: 订单金额计算错误怎么办？**
A: 如果发现金额计算错误，需要取消原订单并重新下单，或者通过退款方式调整差额。

## 相关功能

- [自提单管理](./self-pickup) - 管理客户自提订单
- [退货单管理](./return-order) - 处理订单退货流程
- [代客下单](./proxy-order) - 代替客户创建订单
- [销售报表](../statement/goods-summary) - 查看订单销售统计
- [客户管理](../../customer/manage/customer-list) - 管理订单客户信息
- [库存管理](../../stock/section1/out) - 查看订单出库状态
- [财务管理](../../finance/section1/receivables) - 处理订单收款