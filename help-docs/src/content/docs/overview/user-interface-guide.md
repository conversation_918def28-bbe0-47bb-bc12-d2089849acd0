---
title: 用户界面操作指南
description: 升辉ERP系统用户界面详细操作指南，包括界面布局、导航方式、常用操作和快捷键使用说明
tags: [user-interface, operation-guide, navigation, shortcuts]
keywords: [界面操作, 用户指南, 导航, 快捷键, 升辉ERP]
---

# 用户界面操作指南

本指南将详细介绍升辉ERP系统的用户界面布局、导航方式和常用操作方法，帮助用户快速掌握系统的使用技巧。

## 界面布局概览

### 整体布局结构

升辉ERP系统采用经典的Web应用布局设计，主要包含以下几个区域：

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![系统主界面-经营概况](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/overview-dashboard-main.png) --> --> --> --> --> --> --> --> -->

*图：升辉ERP系统主界面 - 经营概况页面，展示核心业务数据和关键指标*

```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                            │
├─────────────────────────────────────────────────────────┤
│  功能标签页 (概况|商品|订单|客户|采购|库存|财务|报表|应用) │
├──────────┬──────────────────────────────────────────────┤
│          │                                              │
│  左侧    │                                              │
│  菜单    │              主工作区                        │
│  栏      │                                              │
│          │                                              │
└──────────┴──────────────────────────────────────────────┘
```

### 顶部导航栏

**位置**：页面最顶部
**功能**：
- **公司信息**：显示当前登录的公司名称
- **用户信息**：显示当前登录用户的信息和权限
- **系统设置**：访问系统配置和个人设置
- **消息通知**：显示系统通知和待办事项

### 功能标签页

**位置**：顶部导航栏下方
**功能**：系统的主要功能模块入口

| 标签页 | 功能说明 | 主要子模块 |
|--------|----------|------------|
| 概况 | 经营数据概览 | 经营概况、数据统计 |
| 商品 | 商品管理 | 商品列表、分类管理、价格管理 |
| 订单 | 订单处理 | 订单列表、代客下单、订单统计 |
| 客户 | 客户管理 | 客户列表、客户分类、客户分析 |
| 采购 | 采购管理 | 采购订单、供应商管理、采购统计 |
| 库存 | 库存管理 | 出入库、库存查询、盘点管理 |
| 财务 | 财务管理 | 应收应付、收付款、财务报表 |
| 报表 | 数据分析 | 销售报表、客户报表、库存报表 |
| 应用 | 扩展功能 | 营销工具、新零售、第三方应用 |

#### 商品管理界面示例

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![商品列表界面](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/goods-list-main.png) --> --> --> --> --> --> --> --> -->

*图：商品列表管理界面，显示商品信息、库存状态和操作功能*

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![商品分类管理界面](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/goods-category-main.png) --> --> --> --> --> --> --> --> -->

*图：商品分类管理界面，支持分类的层级管理和批量操作*

#### 订单管理界面示例

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![订单列表界面](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/order-list-main.png) --> --> --> --> --> --> --> --> -->

*图：订单列表管理界面，显示订单详情、状态跟踪和批量操作功能*

#### 客户管理界面示例

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![客户列表界面](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/customer-list-main.png) --> --> --> --> --> --> --> --> --> -->

*图：客户列表管理界面，展示客户信息、业务员分配和客户状态管理*

#### 库存管理界面示例

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![出库管理界面](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/stock-outbound-main.png) --> --> --> --> --> --> --> --> -->

*图：出库管理界面，支持多种出库类型和审核流程管理*

#### 财务管理界面示例

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![客户往来汇总表](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/finance-customer-balance-main.png) --> --> --> --> --> --> --> --> -->

*图：客户往来汇总表，提供完整的财务往来记录和余额管理*

#### 报表管理界面示例

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![商品销售报表](/tmp/playwright-mcp-output/2025-07-21T14-53-49.932Z/report-goods-sales-main.png) --> --> --> --> --> --> --> --> -->

*图：商品销售报表界面，提供详细的销售数据分析和统计功能*

### 左侧菜单栏

**位置**：页面左侧
**功能**：当前功能模块的详细子功能菜单
**特点**：
- 鼠标悬停自动展开子菜单
- 支持多级菜单结构
- 当前页面菜单项高亮显示

### 主工作区

**位置**：页面中央主要区域
**功能**：显示具体的业务数据和操作界面
**组成**：
- **面包屑导航**：显示当前页面的层级路径
- **操作工具栏**：常用操作按钮和筛选条件
- **数据展示区**：表格、图表、表单等内容展示
- **分页控件**：数据分页浏览控制

## 导航操作方法

### 基本导航

#### 1. 标签页切换
- **点击标签页**：直接点击顶部的功能标签页切换模块
- **键盘快捷键**：使用 `Ctrl + 数字键` 快速切换（如 Ctrl+1 切换到概况）

#### 2. 菜单导航
- **鼠标悬停**：将鼠标悬停在左侧菜单项上，自动展开子菜单
- **点击菜单项**：点击具体的菜单项进入对应功能页面
- **菜单收缩**：点击菜单栏顶部的收缩按钮可以收起菜单

#### 3. 面包屑导航
- **路径显示**：显示当前页面在系统中的完整路径
- **快速返回**：点击面包屑中的任意层级可以快速返回上级页面

### 高级导航技巧

#### 1. 多标签页浏览
- 系统支持在同一浏览器窗口中打开多个标签页
- 可以在不同功能模块间快速切换而不丢失当前操作状态

#### 2. 收藏夹功能
- 将常用的功能页面添加到收藏夹
- 通过收藏夹快速访问经常使用的功能

#### 3. 搜索导航
- 使用全局搜索功能快速定位到特定的功能或数据
- 支持模糊搜索和智能提示

## 常用操作指南

### 数据查看操作

#### 1. 列表浏览
- **分页浏览**：使用页面底部的分页控件浏览大量数据
- **每页显示数量**：可以调整每页显示的记录数量（10/20/50/100条）
- **跳转到指定页**：在页码输入框中输入页码快速跳转

#### 2. 数据筛选
- **快速筛选**：使用页面上方的筛选条件快速过滤数据
- **高级筛选**：点击"高级筛选"使用更多筛选条件
- **重置筛选**：点击"重置筛选"清除所有筛选条件

#### 3. 数据排序
- **列排序**：点击表格列标题进行升序/降序排序
- **多列排序**：按住Shift键点击多个列标题进行多列排序

### 数据编辑操作

#### 1. 新增数据
- **新增按钮**：点击页面上的"新增"或"添加"按钮
- **快速新增**：使用快捷键 `Ctrl + N` 快速新增
- **批量新增**：支持Excel导入进行批量数据新增

#### 2. 编辑数据
- **行内编辑**：双击表格单元格进行快速编辑
- **表单编辑**：点击"编辑"按钮打开详细编辑表单
- **批量编辑**：选择多条记录进行批量修改

#### 3. 删除数据
- **单条删除**：点击记录行的"删除"按钮
- **批量删除**：选择多条记录后点击"批量删除"
- **软删除**：系统采用软删除机制，删除的数据可以恢复

### 数据导出操作

#### 1. 导出功能
- **当前页导出**：导出当前页面显示的数据
- **全部导出**：导出符合筛选条件的所有数据
- **自定义导出**：选择需要导出的字段和格式

#### 2. 导出格式
- **Excel格式**：最常用的导出格式，支持数据分析
- **PDF格式**：适合打印和存档的格式
- **CSV格式**：纯文本格式，便于数据交换

## 快捷键使用

### 全局快捷键

| 快捷键 | 功能说明 |
|--------|----------|
| `Ctrl + S` | 保存当前编辑内容 |
| `Ctrl + N` | 新增记录 |
| `Ctrl + F` | 打开搜索功能 |
| `Ctrl + E` | 导出数据 |
| `Ctrl + R` | 刷新当前页面 |
| `ESC` | 关闭当前对话框 |
| `Enter` | 确认当前操作 |
| `F5` | 刷新页面数据 |

### 表格操作快捷键

| 快捷键 | 功能说明 |
|--------|----------|
| `↑↓` | 在表格行间移动 |
| `←→` | 在表格列间移动 |
| `Space` | 选择/取消选择当前行 |
| `Ctrl + A` | 全选当前页所有记录 |
| `Delete` | 删除选中的记录 |

### 表单操作快捷键

| 快捷键 | 功能说明 |
|--------|----------|
| `Tab` | 移动到下一个输入字段 |
| `Shift + Tab` | 移动到上一个输入字段 |
| `Ctrl + Enter` | 提交表单 |
| `Ctrl + Z` | 撤销上一步操作 |
| `Ctrl + Y` | 重做操作 |

## 界面个性化设置

### 显示设置

#### 1. 主题设置
- **浅色主题**：适合白天使用的明亮主题
- **深色主题**：适合夜间使用的暗色主题
- **自动切换**：根据系统时间自动切换主题

#### 2. 字体设置
- **字体大小**：可以调整界面字体的大小
- **字体类型**：选择适合的字体类型

#### 3. 布局设置
- **菜单宽度**：调整左侧菜单栏的宽度
- **表格行高**：调整表格行的高度
- **分页大小**：设置默认的分页显示数量

### 功能设置

#### 1. 默认页面
- 设置登录后默认显示的页面
- 可以设置为最常用的功能模块

#### 2. 快捷操作
- 自定义常用操作的快捷键
- 设置快捷操作按钮的显示位置

#### 3. 提醒设置
- 设置各种业务提醒的开关
- 配置提醒的方式和频率

## 移动端使用

### 响应式设计

升辉ERP系统采用响应式设计，可以在各种设备上正常使用：

- **桌面电脑**：完整功能体验
- **平板电脑**：优化的触控操作
- **手机**：核心功能的移动版本

### 移动端特色功能

#### 1. 触控操作
- **滑动操作**：左右滑动切换标签页
- **长按操作**：长按显示上下文菜单
- **双击操作**：双击进入编辑模式

#### 2. 离线功能
- **数据缓存**：重要数据本地缓存
- **离线查看**：无网络时可以查看缓存数据
- **同步更新**：网络恢复后自动同步数据

## 常见问题解决

### 界面显示问题

#### 1. 页面显示不完整
- **解决方法**：调整浏览器缩放比例到100%
- **快捷键**：使用 `Ctrl + 0` 重置缩放

#### 2. 菜单无法展开
- **解决方法**：检查浏览器JavaScript是否启用
- **替代方案**：使用面包屑导航或搜索功能

#### 3. 数据加载缓慢
- **解决方法**：检查网络连接，清除浏览器缓存
- **优化建议**：使用筛选条件减少数据量

### 操作问题

#### 1. 无法保存数据
- **检查项**：确认必填字段已填写
- **权限检查**：确认当前用户有保存权限
- **网络检查**：确认网络连接正常

#### 2. 快捷键不生效
- **浏览器检查**：确认浏览器支持快捷键
- **冲突检查**：检查是否与浏览器快捷键冲突
- **焦点检查**：确认当前焦点在正确的元素上

---

通过本指南的学习，您应该能够熟练使用升辉ERP系统的各种界面功能。如果在使用过程中遇到问题，请参考相关功能模块的详细文档或联系技术支持。
