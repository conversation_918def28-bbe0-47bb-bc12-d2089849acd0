---
title: 批次流水
description: 批次流水查询与管理功能指南
---

# 批次流水

批次流水模块用于追踪和管理商品按批次的库存变动记录，帮助企业精确掌握每个商品批次的去向和库存状态，实现批次级别的库存精细化管理。

## 功能概述

批次流水模块提供以下主要功能：

- **批次记录查询**：查询指定时间段内各批次的库存变动记录
- **按商品筛选**：支持按商品名称进行搜索特定商品的批次记录
- **按仓库筛选**：支持按特定仓库查看批次流水
- **日期范围筛选**：支持按日期范围查询批次记录
- **数据导出**：支持导出批次流水数据进行进一步分析

## 操作指南

### 1. 进入批次流水页面

在系统左侧导航栏中，点击【库存】→【批次流水】，进入批次流水管理页面。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![批次流水主界面](../../../../assets/stock-batch/batch_flow_main.png) --> --> --> --> --> --> --> --> --> -->

### 2. 按商品查询

在页面上方的搜索框中，输入商品名称进行查询：

1. 点击搜索框
2. 输入商品名称
3. 点击搜索按钮或按回车键确认

<!-- <!-- <!-- ![批次流水商品搜索](../../../../assets/stock-batch/batch_flow_search.png) --> --> -->

### 3. 按仓库筛选

通过仓库下拉框，可以筛选特定仓库的批次流水：

1. 点击"残品仓"下拉框
2. 在弹出的选项中选择目标仓库
3. 系统将自动刷新并显示所选仓库的批次流水数据

<!-- ![批次流水仓库筛选](../../../../assets/stock-batch/batch_flow_warehouse_filter.png) -->

### 4. 按日期筛选

通过日期选择器，可以筛选特定时间段内的批次流水：

1. 点击日期选择区域
2. 在弹出的日历中选择制单开始日期和结束日期
3. 系统将自动刷新并显示所选时间段内的批次流水数据

<!-- <!-- <!-- ![批次流水日期筛选](../../../../assets/stock-batch/batch_flow_date_picker.png) --> --> -->

### 5. 导出数据

通过导出功能，可以将当前筛选条件下的批次流水数据导出为Excel文件：

1. 设置好筛选条件
2. 点击右上角的"导出"按钮
3. 系统将自动下载Excel格式的批次流水数据文件

<!-- <!-- <!-- ![批次流水导出](../../../../assets/stock-batch/batch_flow_export.png) --> --> -->

## 批次流水与普通库存流水的区别

批次流水相比普通库存流水，具有以下特点：

1. **更精细的跟踪粒度**：按批次号追踪商品，能精确到每一个批次的出入库记录
2. **批次号管理**：显示每条记录的批次号，方便追溯商品批次信息
3. **质量管理支持**：便于实现先进先出、保质期管理等高级库存管理功能
4. **溯源管理**：支持商品从生产到销售的全流程跟踪

## 注意事项

- 批次流水记录包含各类带有批次信息的出入库操作
- 使用批次流水可以有效支持食品、药品等需要批次管理的商品
- 批次号与库存变动情况密切相关，应定期检查批次完整性
- 导出功能受查询结果数量限制，如需导出大量数据，请分批次操作

