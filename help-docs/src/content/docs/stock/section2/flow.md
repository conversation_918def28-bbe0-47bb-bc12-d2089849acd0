---
title: 库存流水
description: 库存流水查询与管理功能指南
---

# 库存流水

库存流水模块用于查询和管理商品在各个仓库的出入库记录，帮助用户追踪商品库存变动情况，分析商品流通情况，为库存管理提供数据支持。

## 功能概述

库存流水模块提供以下主要功能：

- **流水记录查询**：查询指定时间段内的库存变动记录
- **按商品筛选**：支持按商品名称/编码进行搜索
- **按仓库筛选**：支持按特定仓库查看库存流水
- **按单据类型筛选**：区分不同类型的出入库单据
- **数据导出**：支持导出库存流水数据进行进一步分析

## 操作指南

### 1. 进入库存流水页面

在系统左侧导航栏中，点击【库存】→【库存流水】，进入库存流水管理页面。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![库存流水主界面](../../../../assets/stock-flow/stock_flow_main.png) --> --> --> --> --> --> --> --> --> -->

### 2. 按商品查询

在页面上方的搜索框中，输入商品名称或编码进行查询：

1. 点击搜索框
2. 输入商品名称或编码
3. 点击搜索按钮或按回车键确认

<!-- <!-- <!-- ![库存流水搜索](../../../../assets/stock-flow/stock_flow_search.png) --> --> -->

### 3. 按仓库筛选

通过仓库下拉框，可以筛选特定仓库的库存流水：

1. 点击"残品仓"下拉框
2. 在弹出的选项中选择目标仓库
3. 系统将自动刷新并显示所选仓库的库存流水数据

<!-- ![库存流水仓库筛选](../../../../assets/stock-flow/stock_flow_warehouse_filter.png) -->

### 4. 按日期筛选

通过日期选择器，可以筛选特定时间段内的库存流水：

1. 点击日期选择区域
2. 在弹出的日历中选择起始日期和结束日期
3. 系统将自动刷新并显示所选时间段内的库存流水数据

<!-- <!-- <!-- ![库存流水日期筛选](../../../../assets/stock-flow/stock_flow_date_picker.png) --> --> -->

### 5. 导出数据

通过导出功能，可以将当前筛选条件下的库存流水数据导出为Excel文件：

1. 设置好筛选条件
2. 点击右上角的"导出"按钮
3. 系统将自动下载Excel格式的库存流水数据文件

<!-- <!-- <!-- ![库存流水导出](../../../../assets/stock-flow/stock_flow_export.png) --> --> -->

## 注意事项

- 库存流水记录包含各类出入库操作，如采购入库、销售出库、调拨、盘点等
- 日期范围过大可能会导致数据量过大，建议适当缩小查询范围
- 导出功能受查询结果数量限制，如需导出大量数据，请分批次操作

