---
title: 仓库库存
description: 仓库库存功能使用指南
---

# 仓库库存

仓库库存功能用于查看和管理各个仓库中商品的库存情况，帮助用户快速了解不同仓库的库存分布，便于进行库存调配和管理决策。

## 功能概述

仓库库存主要提供以下功能：

1. **库存查询**：按仓库查看各商品的库存情况
2. **数据导出**：支持导出库存数据为Excel文件
3. **库存筛选**：根据商品名称、规格等条件筛选库存信息

## 操作指南

### 1. 查看仓库库存

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![仓库库存主界面](../../../../assets/warehouse-inventory/main.png) --> --> --> --> --> --> --> --> -->

仓库库存页面显示所有仓库的库存信息，包括：
- 商品编码
- 商品名称
- 规格
- 仓库库存数量
- 换算比例和换算库存等信息

### 2. 搜索特定商品

<!-- <!-- ![搜索功能](../../../../assets/warehouse-inventory/search.png) --> -->

在页面上方的搜索框中输入商品名称或商品编码，可以快速筛选出相关商品的库存信息。系统支持模糊搜索，便于快速定位商品。

### 3. 导出库存数据

<!-- <!-- ![导出功能](../../../../assets/warehouse-inventory/export.png) --> -->

点击页面右上角的"导出"按钮，系统会将当前筛选条件下的所有库存数据导出为Excel文件，便于离线分析和报表生成。

## 注意事项

1. 仓库库存数据实时更新，反映最新的库存状态
2. 导出的数据为导出时刻的库存状态，不会随系统更新而变化
3. 库存数量为0的商品默认也会显示，便于全面了解商品分布情况
4. 如需调整库存，请使用调拨单、盘点单等专门的功能进行操作

## 相关功能

- [库存查询](/stock/section2/query)：查询整体库存情况
- [库存流水](/stock/section2/flow)：查看库存变动记录
- [批次流水](/stock/section2/batch)：查看按批次管理的库存流水
- [调拨单](/stock/section3/allocate)：在不同仓库间调拨商品 