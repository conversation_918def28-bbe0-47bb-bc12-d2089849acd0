---
title: 供应商库存流水
description: 供应商库存流水查询与管理功能指南
---

# 供应商库存流水

供应商库存流水模块用于查询和管理供应商寄存商品的库存变动记录，帮助企业和供应商精确追踪库存变化，提高供应链透明度。

## 功能概述

供应商库存流水模块提供以下主要功能：

- **流水查询**：查询供应商库存变动的详细记录
- **按商品筛选**：支持按商品名称进行搜索
- **按仓库筛选**：支持按特定仓库查看供应商库存流水
- **按时间查询**：支持按日期范围查询库存变动记录
- **数据导出**：支持导出供应商库存流水数据

## 操作指南

### 1. 进入供应商库存流水页面

在系统左侧导航栏中，点击【库存】→【供应商】→【库存流水】，进入供应商库存流水页面。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![供应商库存流水主界面](../../../../assets/supplier-inventory-flowing/supplier_inventory_flowing_main.png) --> --> --> --> --> --> --> --> --> -->

### 2. 按商品查询

在页面上方的搜索框中，输入商品名称进行查询：

1. 点击搜索框
2. 输入商品名称或编码
3. 点击搜索按钮或按回车键确认

<!-- <!-- <!-- ![供应商库存流水商品搜索](../../../../assets/supplier-inventory-flowing/supplier_inventory_flowing_search.png) --> --> -->

### 3. 按仓库筛选

通过仓库下拉框，可以筛选特定仓库的供应商库存流水：

1. 点击仓库下拉框
2. 在弹出的选项中选择目标仓库
3. 系统将自动刷新并显示所选仓库的供应商库存流水数据

<!-- ![供应商库存流水仓库筛选](../../../../assets/supplier-inventory-flowing/supplier_inventory_flowing_warehouse_filter.png) -->

### 4. 按日期筛选

通过日期选择器，可以查询指定时间范围内的库存流水记录：

1. 点击日期选择器
2. 在弹出的日历中选择开始日期和结束日期
3. 系统将自动刷新并显示所选时间范围内的供应商库存流水数据

<!-- <!-- <!-- <!-- ![供应商库存流水日期筛选](../../../../assets/supplier-inventory-flowing/supplier_inventory_flowing_date_filter.png) --> --> --> -->

### 5. 导出数据

通过导出功能，可以将当前筛选条件下的供应商库存流水数据导出为Excel文件：

1. 设置好筛选条件
2. 点击右上角的"导出"按钮
3. 系统将自动下载Excel格式的供应商库存流水数据文件

<!-- <!-- <!-- ![供应商库存流水导出](../../../../assets/supplier-inventory-flowing/supplier_inventory_flowing_export.png) --> --> -->

### 6. 申请付款

针对特定条件下的库存数据，可以进行付款申请：

1. 设置好筛选条件
2. 点击"申请付款"按钮
3. 根据系统提示完成付款申请流程

## 供应商库存流水信息说明

供应商库存流水记录包含以下主要信息：

- **单据编号**：关联的出入库单据编号
- **制单时间**：单据的创建时间
- **单据类型**：库存变动的业务类型，如入库、出库等
- **商品名称**：发生变动的商品名称
- **规格**：商品的规格型号
- **出库/入库**：商品的出入库数量
- **剩余量**：变动后的剩余库存量
- **其他单位**：商品的辅助计量单位
- **平均成本**：商品的平均成本
- **支付状态**：付款状态标识

## 注意事项

- 供应商库存流水数据只包含供应商所有的商品库存变动记录
- 库存流水记录按照时间先后顺序排列，便于追踪库存变化
- 通过单据编号可以关联到原始业务单据查看更多详情
- 库存变动记录一经生成不可修改，确保数据准确性 