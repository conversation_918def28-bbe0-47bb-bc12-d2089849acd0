---
title: 库存盘点管理
description: 学习如何使用库存盘点功能，包括盘点计划制定、盘点执行、差异处理等完整的盘点管理流程
tags: [stock, inventory, stocktake, audit, management]
keywords: [库存盘点, 盘点单, 库存审计, 差异处理, 库存管理]
---

# 库存盘点管理

库存盘点是库存管理中的重要环节，通过定期盘点确保系统库存数据与实际库存的一致性。升辉ERP系统提供完整的盘点管理功能，支持全盘、抽盘、循环盘点等多种盘点方式，帮助企业建立规范的库存管理体系。

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![库存盘点主界面](../../../../assets/stock/stocktake/stocktake-main.png) --> --> --> --> --> --> --> --> -->

## 功能特点

1. **多种盘点方式**：支持全盘、抽盘、循环盘点等不同盘点策略
2. **盘点计划管理**：制定和执行有序的盘点计划
3. **实时盘点执行**：支持移动端盘点，提高盘点效率
4. **差异分析处理**：自动计算盘点差异，支持差异原因分析
5. **盘点结果审核**：多级审核机制，确保盘点结果准确性
6. **历史记录追踪**：完整的盘点历史记录，便于分析和审计

## 盘点类型说明

### 盘点方式分类

| 盘点类型 | 说明 | 适用场景 | 频率建议 |
|----------|------|----------|----------|
| 全盘 | 对所有商品进行全面盘点 | 年度盘点、重大节点 | 年度1-2次 |
| 抽盘 | 对部分商品进行抽样盘点 | 日常监控、风险控制 | 月度或季度 |
| 循环盘点 | 按计划轮流盘点不同商品 | 持续监控、均衡工作量 | 每周或每月 |
| 动态盘点 | 针对异常商品的临时盘点 | 异常处理、问题排查 | 按需进行 |

### 盘点范围分类

| 范围类型 | 说明 | 特点 |
|----------|------|------|
| 全仓盘点 | 对整个仓库进行盘点 | 全面、准确、工作量大 |
| 分区盘点 | 按仓库区域分批盘点 | 灵活、可控、便于管理 |
| 分类盘点 | 按商品分类进行盘点 | 专业、高效、便于分析 |
| 重点盘点 | 对重要商品进行盘点 | 针对性强、效率高 |

## 访问路径

1. 在左侧导航菜单中，点击**库存**
2. 在展开的子菜单中，选择**仓库管理**
3. 点击**盘点单**

## 盘点流程管理

### 盘点流程概览

```mermaid
graph TD
    A[制定盘点计划] --> B[创建盘点单]
    B --> C[分配盘点任务]
    C --> D[执行盘点]
    D --> E[录入盘点结果]
    E --> F[差异分析]
    F --> G{差异是否合理?}
    G -->|是| H[审核通过]
    G -->|否| I[重新盘点]
    I --> D
    H --> J[调整库存]
    J --> K[完成盘点]
```

### 盘点准备阶段

#### 1. 制定盘点计划

**盘点计划要素：**
- **盘点时间**：选择业务相对较少的时间段
- **盘点范围**：确定盘点的商品范围和仓库区域
- **盘点人员**：安排足够的盘点人员和监督人员
- **盘点方式**：选择适合的盘点方式和工具

**计划制定步骤：**
1. 分析库存状况和业务特点
2. 确定盘点的目标和重点
3. 制定详细的盘点时间表
4. 准备盘点所需的工具和设备

#### 2. 盘点前准备

**系统准备：**
- 确保库存数据的完整性和准确性
- 处理完所有待处理的出入库单据
- 冻结盘点期间的库存变动

**现场准备：**
- 整理仓库，确保商品摆放有序
- 准备盘点工具（盘点机、标签、记录表等）
- 培训盘点人员，明确盘点要求

## 操作指南

### 创建盘点单

#### 新建盘点单

1. **进入创建界面**
   - 在盘点单列表页面点击"新增盘点单"
   - 系统弹出盘点单创建表单

2. **填写基本信息**

   ![创建盘点单表单](../../../../assets/stock/stocktake/create-stocktake.png)

   **必填字段：**
   - **盘点单号**：系统自动生成或手动输入
   - **盘点仓库**：选择要盘点的仓库
   - **盘点类型**：选择盘点方式（全盘/抽盘/循环盘点）
   - **盘点日期**：设置盘点执行日期
   - **盘点负责人**：指定盘点负责人

   **可选字段：**
   - **盘点范围**：设置具体的盘点范围
   - **商品筛选**：按分类、品牌等条件筛选商品
   - **备注说明**：添加盘点的特殊说明

3. **选择盘点商品**

   ![选择盘点商品](../../../../assets/stock/stocktake/select-products.png)

   **选择方式：**
   - **全部商品**：选择仓库内所有商品
   - **按分类选择**：选择特定分类的商品
   - **按条件筛选**：根据库存量、价值等条件筛选
   - **手动选择**：逐个选择需要盘点的商品

4. **保存盘点单**
   - 确认信息无误后点击"保存"
   - 盘点单创建成功，状态为"待盘点"

### 执行盘点

#### 盘点任务分配

1. **分配盘点人员**
   - 为每个盘点区域或商品分类分配盘点人员
   - 设置盘点人员的权限和职责范围

2. **下发盘点任务**
   - 将盘点单分解为具体的盘点任务
   - 通过系统或移动端下发给盘点人员

#### 现场盘点操作

1. **移动端盘点**

   ![移动端盘点界面](../../../../assets/stock/stocktake/mobile-stocktake.png)

   **操作步骤：**
   - 使用移动设备登录系统
   - 扫描商品条码或手动输入商品编码
   - 录入实际盘点数量
   - 添加盘点备注（如有异常）

2. **PC端盘点**
   - 在盘点单详情页面录入盘点结果
   - 支持批量录入和Excel导入
   - 实时查看盘点进度

#### 盘点数据录入

1. **录入盘点数量**
   - 准确录入每个商品的实际数量
   - 注意区分不同规格和批次的商品

2. **异常情况处理**
   - 对于无法盘点的商品，标记原因
   - 对于有疑问的商品，添加备注说明

### 差异分析处理

#### 盘点差异计算

系统自动计算以下差异数据：

| 差异类型 | 计算公式 | 说明 |
|----------|----------|------|
| 数量差异 | 实盘数量 - 账面数量 | 正数为盘盈，负数为盘亏 |
| 金额差异 | 数量差异 × 商品成本价 | 差异对应的金额影响 |
| 差异率 | 数量差异 ÷ 账面数量 × 100% | 差异的相对比例 |

#### 差异分析报告

![差异分析报告](../../../../assets/stock/stocktake/difference-analysis.png)

**分析维度：**
- **按商品分析**：查看每个商品的盘点差异
- **按分类分析**：分析不同商品分类的差异情况
- **按仓库分析**：对比不同仓库的盘点准确性
- **按盘点人员分析**：评估盘点人员的工作质量

#### 差异原因分析

**常见差异原因：**

| 差异类型 | 可能原因 | 处理建议 |
|----------|----------|----------|
| 盘盈 | 未及时录入入库、退货未处理 | 检查单据处理情况 |
| 盘亏 | 商品损耗、盗失、出库未录入 | 调查具体原因，加强管控 |
| 大额差异 | 计量错误、系统错误 | 重新盘点，检查系统设置 |
| 批次差异 | 批次管理不规范 | 完善批次管理流程 |

### 盘点审核

#### 审核流程

1. **初审**
   - 盘点负责人对盘点结果进行初步审核
   - 检查盘点数据的完整性和合理性

2. **复审**
   - 仓库主管或财务人员进行复审
   - 重点关注大额差异和异常情况

3. **终审**
   - 管理层对重大差异进行最终审核
   - 决定是否接受盘点结果

#### 审核要点

**数据完整性检查：**
- 所有商品是否都已盘点
- 盘点数据是否录入完整
- 异常情况是否有合理说明

**差异合理性分析：**
- 差异是否在可接受范围内
- 差异原因是否合理
- 是否需要重新盘点

### 库存调整

#### 调整原则

1. **审核通过后调整**：只有审核通过的盘点结果才能调整库存
2. **分批调整**：可以按商品分类或差异大小分批调整
3. **记录完整**：所有调整都要有完整的记录和说明

#### 调整操作

1. **自动调整**
   - 系统根据盘点结果自动生成库存调整单
   - 一键执行库存调整

2. **手动调整**
   - 对于特殊情况，支持手动调整
   - 需要提供详细的调整原因

## 盘点报表分析

### 盘点汇总报表

![盘点汇总报表](../../../../assets/stock/stocktake/summary-report.png)

**报表内容：**
- 盘点商品总数和盘点完成率
- 盘盈盘亏商品数量和金额
- 差异率统计和分析
- 盘点效率和质量指标

### 差异明细报表

**报表功能：**
- 详细列出每个商品的盘点差异
- 支持按多种维度筛选和排序
- 可导出详细数据进行进一步分析

### 盘点历史分析

**分析内容：**
- 历次盘点的差异趋势
- 不同商品的盘点准确性
- 盘点质量的持续改进情况

## 权限管理

| 操作 | 所需权限 | 说明 |
|------|----------|------|
| 创建盘点单 | 库存管理-盘点创建 | 可以创建新的盘点单 |
| 执行盘点 | 库存管理-盘点执行 | 可以录入盘点数据 |
| 审核盘点 | 库存管理-盘点审核 | 可以审核盘点结果 |
| 调整库存 | 库存管理-库存调整 | 可以根据盘点结果调整库存 |
| 查看报表 | 库存管理-报表查看 | 可以查看盘点相关报表 |

## 最佳实践

### 盘点计划制定

1. **定期盘点**：建立定期盘点制度，如月度抽盘、季度重点盘、年度全盘
2. **重点关注**：对高价值、快周转、易损耗商品增加盘点频次
3. **合理安排**：避开业务高峰期，确保盘点质量

### 盘点执行管理

1. **人员培训**：定期培训盘点人员，提高盘点技能和责任意识
2. **工具准备**：使用先进的盘点工具，提高盘点效率和准确性
3. **现场管理**：加强盘点现场管理，确保盘点环境有序

### 差异处理原则

1. **及时处理**：发现差异及时分析原因，快速处理
2. **根因分析**：深入分析差异产生的根本原因，制定改进措施
3. **持续改进**：建立差异分析机制，持续改进库存管理

## 注意事项

### 盘点准备

1. **数据准备**：盘点前确保所有出入库单据已处理完毕
2. **现场准备**：整理仓库，确保商品摆放规范有序
3. **人员安排**：合理安排盘点人员，确保盘点质量

### 盘点执行

1. **准确录入**：确保盘点数据录入准确，避免人为错误
2. **异常标记**：对异常情况及时标记和说明
3. **进度监控**：实时监控盘点进度，确保按时完成

### 结果处理

1. **审慎调整**：库存调整要审慎，确保调整的合理性
2. **记录完整**：保持完整的盘点记录，便于后续分析
3. **经验总结**：及时总结盘点经验，改进盘点流程

## 常见问题

### Q: 盘点期间可以进行出入库操作吗？
A: 建议在盘点期间暂停相关商品的出入库操作，或者：
- 设置库存冻结，禁止库存变动
- 记录盘点期间的所有库存变动
- 在盘点结果中考虑这些变动

### Q: 如何处理盘点中发现的大额差异？
A: 对于大额差异，建议：
- 立即停止相关商品的盘点
- 重新核查盘点数据和方法
- 调查差异产生的具体原因
- 必要时进行重新盘点

### Q: 盘点结果可以撤销吗？
A: 已审核通过并调整库存的盘点结果一般不建议撤销，如确需撤销：
- 需要管理层审批
- 要有充分的撤销理由
- 撤销后需要重新盘点

### Q: 如何提高盘点效率？
A: 可以采取以下措施：
- 使用移动盘点设备和条码扫描
- 合理分配盘点任务和人员
- 优化仓库布局和商品摆放
- 建立标准化的盘点流程

## 相关功能

- [库存查询](../section2/query) - 查看当前库存状况
- [库存流水](../section2/flow) - 查看库存变动记录
- [仓库管理](./warehouse) - 管理仓库基础信息
- [报损单](./loss-report) - 处理商品损耗

