---
title: 库位管理
description: 如何管理仓库的库位，包括新增、编辑和查看库位信息
---

# 库位管理

库位管理是仓库管理系统中的重要组成部分，用于管理仓库内部具体的存放位置。通过库位管理，您可以更精细地控制商品存放位置，提高仓储管理效率。

## 功能简介

- **库位列表查看**：查看所有库位信息，包括编码、名称、所属库区等
- **库位新增**：添加新的库位
- **库位编辑**：修改现有库位信息
- **库位状态管理**：启用或停用库位

## 操作指南

### 查看库位列表

1. 进入"仓库管理"->"库位管理"菜单
2. 系统将显示所有库位信息，包括ID、库位编码、库位名称、所属库区等信息

<!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- <!-- ![库位管理主界面](../../../../assets/location-management/location_management_main.png) --> --> --> --> --> --> --> --> --> -->

### 新增库位

1. 在库位管理页面，点击右上角【新增库位】按钮
2. 在弹出的表单中填写以下信息：
   - 所属库区：选择该库位所属的库区
   - 库位名称：输入库位的名称
   - 库位编码：输入库位的唯一编码
3. 点击【确定】按钮提交

<!-- <!-- ![新增库位表单](../../../../assets/location-management/location_management_add.png) --> -->

#### 选择所属库区

1. 点击所属库区的下拉框
2. 从列表中选择相应的库区

<!-- ![选择库区](../../../../assets/location-management/location_management_select_area.png) -->

#### 填写库位信息

1. 在库位名称字段中输入名称（例如：测试库位名称）
2. 在库位编码字段中输入编码（例如：test123）
3. 确保所有必填项都已填写
4. 点击【确定】按钮保存

<!-- ![填写库位信息](../../../../assets/location-management/location_management_form_filled.png) -->

### 库位管理操作

库位列表中，每条记录右侧提供了操作按钮：

- **编辑**：修改库位信息
- **二维码**：查看库位二维码信息

### 状态管理

每个库位都有状态指示器，显示当前库位是启用还是停用状态。绿色表示启用状态，可以通过点击切换状态。

## 注意事项

1. 库位编码必须唯一，不可重复
2. 库位必须关联到已存在的库区
3. 一旦库位被使用（有库存记录关联），建议不要随意修改库位信息
4. 如果需要停用某个库位，请确保该库位没有活动库存
