---
title: 库区管理
description: 库区管理功能介绍与操作指南
---

# 库区管理

库区管理模块是系统仓库管理的重要组成部分，用于管理仓库内部的区域划分。通过库区管理，用户可以将仓库划分为不同的库区，有效提高仓库空间利用率和库存管理效率。

## 功能简介

库区管理主要提供以下功能：

1. **库区信息查看**：查看所有库区的基本信息，包括库区编码、库区名称、所属仓库、尺寸信息和限重等。
2. **新增库区**：添加新的库区，设置库区的基本信息。
3. **编辑库区**：修改现有库区的信息。
4. **删除库区**：删除不再使用的库区。

## 操作指南

### 查看库区列表

进入系统后，通过以下路径访问库区管理：

1. 点击左侧导航栏的【库存】菜单
2. 在展开的菜单中，选择【仓库管理】-【库区管理】

<!-- <!-- <!-- ![库区管理主界面](../../../../assets/warehouse-area/main-page.png) --> --> -->

系统将显示所有库区的列表，包含以下信息：
- ID：库区的唯一标识
- 库区编码：库区的编码
- 库区名称：库区的名称
- 所属仓库：库区所在的仓库
- 长/米：库区的长度
- 宽/米：库区的宽度
- 高/米：库区的高度
- 库区限重/kg：库区的最大承重
- 库区类型：库区的类型
- 状态：库区的使用状态
- 操作：可对库区进行的操作

### 新增库区

1. 在库区管理页面，点击右上角的【新增库区】按钮
2. 在弹出的新增库区窗口中，填写库区信息：

<!-- ![新增库区](../../../../assets/warehouse-area/add-warehouse-area.png) -->

3. 填写以下必填信息：
   - 所属仓库：从下拉菜单中选择库区所属的仓库
   
   <!-- ![选择所属仓库](../../../../assets/warehouse-area/warehouse-dropdown.png) -->
   
   - 库区名称：输入库区的名称
   - 库区编码：输入库区的唯一编码
   - 库区类型：选择库区的类型
   
4. 填写尺寸信息（可选）：
   - 长：库区的长度，单位为米
   - 宽：库区的宽度，单位为米
   - 高：库区的高度，单位为米
   - 库区限重：库区的最大承重，单位为千克
   
   <!-- ![填写完整信息](../../../../assets/warehouse-area/form-dimensions.png) -->

5. 填写完成后，点击【确定】按钮保存
6. 系统将新增的库区添加到列表中

<!-- ![新增库区成功](../../../../assets/warehouse-area/added-warehouse-area.png) -->

### 编辑库区

1. 在库区列表中，找到需要修改的库区，点击操作列中的【编辑】按钮
2. 在弹出的修改库区窗口中，修改库区信息

<!-- ![编辑库区](../../../../assets/warehouse-area/edit-warehouse-area.png) -->

3. 修改完成后，点击【确定】按钮保存
4. 系统将更新库区信息

<!-- ![更新库区成功](../../../../assets/warehouse-area/updated-warehouse-area.png) -->

### 删除库区

1. 在库区列表中，找到需要删除的库区，点击操作列中的【删除】按钮
2. 系统将直接删除该库区（注意：请谨慎操作，删除后无法恢复）

## 注意事项

1. 库区编码必须唯一，不能与已有库区重复
2. 删除库区前，请确保该库区内没有库存，否则可能导致库存管理混乱
3. 库区的尺寸和限重信息可以为空，但建议填写以便于更好地管理库存
4. 所属仓库一旦设置，通常不建议修改，如需调整请谨慎操作
