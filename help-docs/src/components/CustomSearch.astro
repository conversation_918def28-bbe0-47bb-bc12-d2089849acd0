---
// 自定义搜索组件，在开发环境中提供更好的用户体验
import Default from '@astrojs/starlight/components/Search.astro';

// 检查是否在开发环境
const isDev = import.meta.env.DEV;
---

{isDev ? (
  <!-- 开发环境中的自定义搜索界面 -->
  <div class="custom-search-wrapper">
    <button 
      type="button" 
      class="custom-search-button"
      onclick="document.getElementById('dev-search-modal').showModal()"
    >
      <svg aria-hidden="true" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M21.71 20.29 18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.39ZM11 18a7 7 0 1 1 7-7 7 7 0 0 1-7 7Z"/>
      </svg>
      <span>搜索文档</span>
      <kbd>⌘K</kbd>
    </button>

    <!-- 开发环境搜索模态框 -->
    <dialog id="dev-search-modal" class="dev-search-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>文档搜索</h3>
          <button 
            type="button" 
            class="close-button"
            onclick="document.getElementById('dev-search-modal').close()"
          >
            ✕
          </button>
        </div>
        
        <div class="search-notice">
          <div class="notice-icon">ℹ️</div>
          <div class="notice-content">
            <h4>开发环境提示</h4>
            <p>搜索功能仅在生产版本中可用。在开发环境中，您可以：</p>
            <ul>
              <li>使用左侧导航菜单浏览文档</li>
              <li>运行 <code>pnpm build && pnpm preview</code> 来测试搜索功能</li>
              <li>使用下方的快速导航链接</li>
            </ul>
          </div>
        </div>

        <div class="quick-nav">
          <h4>快速导航</h4>
          <div class="nav-grid">
            <a href="/overview/demo/" class="nav-item">
              <span class="nav-icon">🎯</span>
              <span class="nav-label">系统演示</span>
            </a>
            <a href="/customer/manage/customer-list/" class="nav-item">
              <span class="nav-icon">👥</span>
              <span class="nav-label">客户管理</span>
            </a>
            <a href="/order/manage/order-list/" class="nav-item">
              <span class="nav-icon">📋</span>
              <span class="nav-label">订单管理</span>
            </a>
            <a href="/stock/section2/query/" class="nav-item">
              <span class="nav-icon">📦</span>
              <span class="nav-label">库存查询</span>
            </a>
            <a href="/finance/section1/customer-summary/" class="nav-item">
              <span class="nav-icon">💰</span>
              <span class="nav-label">财务管理</span>
            </a>
            <a href="/marketing/bill-template/bill-template/" class="nav-item">
              <span class="nav-icon">🎯</span>
              <span class="nav-label">营销系统</span>
            </a>
          </div>
        </div>
      </div>
    </dialog>
  </div>

  <style>
    .custom-search-wrapper {
      position: relative;
    }

    .custom-search-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: var(--sl-color-gray-6);
      border: 1px solid var(--sl-color-gray-5);
      border-radius: 0.5rem;
      color: var(--sl-color-gray-2);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.875rem;
    }

    .custom-search-button:hover {
      background: var(--sl-color-gray-5);
      border-color: var(--sl-color-accent);
    }

    .custom-search-button kbd {
      background: var(--sl-color-gray-5);
      border: 1px solid var(--sl-color-gray-4);
      border-radius: 0.25rem;
      padding: 0.125rem 0.375rem;
      font-size: 0.75rem;
      font-family: monospace;
    }

    .dev-search-modal {
      border: none;
      border-radius: 1rem;
      padding: 0;
      max-width: 600px;
      width: 90vw;
      background: var(--sl-color-bg);
      color: var(--sl-color-text);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .dev-search-modal::backdrop {
      background: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      padding: 1.5rem;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .modal-header h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
    }

    .close-button {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: var(--sl-color-gray-3);
      padding: 0.25rem;
      border-radius: 0.25rem;
    }

    .close-button:hover {
      background: var(--sl-color-gray-6);
    }

    .search-notice {
      display: flex;
      gap: 1rem;
      padding: 1rem;
      background: var(--sl-color-blue-low);
      border: 1px solid var(--sl-color-blue);
      border-radius: 0.5rem;
      margin-bottom: 1.5rem;
    }

    .notice-icon {
      font-size: 1.5rem;
      flex-shrink: 0;
    }

    .notice-content h4 {
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
      font-weight: 600;
    }

    .notice-content p {
      margin: 0 0 0.75rem 0;
      line-height: 1.5;
    }

    .notice-content ul {
      margin: 0;
      padding-left: 1.25rem;
    }

    .notice-content li {
      margin-bottom: 0.25rem;
    }

    .notice-content code {
      background: var(--sl-color-gray-6);
      padding: 0.125rem 0.375rem;
      border-radius: 0.25rem;
      font-size: 0.875rem;
    }

    .quick-nav h4 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      font-weight: 600;
    }

    .nav-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 0.75rem;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem;
      background: var(--sl-color-gray-6);
      border: 1px solid var(--sl-color-gray-5);
      border-radius: 0.5rem;
      text-decoration: none;
      color: var(--sl-color-text);
      transition: all 0.2s ease;
    }

    .nav-item:hover {
      background: var(--sl-color-gray-5);
      border-color: var(--sl-color-accent);
      transform: translateY(-1px);
    }

    .nav-icon {
      font-size: 1.25rem;
      flex-shrink: 0;
    }

    .nav-label {
      font-weight: 500;
    }

    /* 键盘快捷键支持 */
    @media (max-width: 768px) {
      .custom-search-button kbd {
        display: none;
      }
    }
  </style>

  <script>
    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('dev-search-modal').showModal();
      }
      if (e.key === 'Escape') {
        document.getElementById('dev-search-modal').close();
      }
    });

    // 点击模态框外部关闭
    document.getElementById('dev-search-modal').addEventListener('click', (e) => {
      if (e.target === e.currentTarget) {
        e.currentTarget.close();
      }
    });
  </script>
) : (
  <!-- 生产环境使用默认搜索 -->
  <Default><slot /></Default>
)}
