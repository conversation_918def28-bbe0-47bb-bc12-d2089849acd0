---
// 返回顶部按钮组件
// 当用户滚动到一定距离时显示，点击平滑滚动到页面顶部
---

<button 
  id="back-to-top" 
  class="back-to-top-btn" 
  aria-label="返回顶部"
  title="返回顶部"
>
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
  </svg>
</button>

<style>
  .back-to-top-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background: var(--sl-color-accent);
    color: var(--sl-color-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
  }

  .back-to-top-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .back-to-top-btn:hover {
    background: var(--sl-color-accent-high);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .back-to-top-btn:active {
    transform: translateY(0);
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .back-to-top-btn {
      bottom: 1.5rem;
      right: 1.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }

    .back-to-top-btn svg {
      width: 16px;
      height: 16px;
    }
  }

  /* 减少动画效果（用户偏好） */
  @media (prefers-reduced-motion: reduce) {
    .back-to-top-btn {
      transition: opacity 0.2s ease;
    }

    .back-to-top-btn:hover {
      transform: none;
    }
  }

  /* 高对比度模式适配 */
  @media (prefers-contrast: high) {
    .back-to-top-btn {
      border: 2px solid var(--sl-color-text);
    }
  }
</style>

<script>
  // 返回顶部功能实现
  function initBackToTop() {
    const backToTopBtn = document.getElementById('back-to-top');
    if (!backToTopBtn) return;

    // 滚动阈值（像素）
    const scrollThreshold = 300;

    // 监听滚动事件
    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      if (scrollTop > scrollThreshold) {
        backToTopBtn.classList.add('visible');
      } else {
        backToTopBtn.classList.remove('visible');
      }
    }

    // 平滑滚动到顶部
    function scrollToTop() {
      // 检查是否支持平滑滚动
      if ('scrollBehavior' in document.documentElement.style) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } else {
        // 降级处理：使用动画滚动
        const scrollStep = -window.scrollY / (500 / 15);
        const scrollInterval = setInterval(() => {
          if (window.scrollY !== 0) {
            window.scrollBy(0, scrollStep);
          } else {
            clearInterval(scrollInterval);
          }
        }, 15);
      }
    }

    // 节流函数，优化滚动性能
    function throttle(func: Function, limit: number) {
      let inThrottle: boolean;
      return function(this: any) {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    }

    // 绑定事件
    window.addEventListener('scroll', throttle(handleScroll, 100));
    backToTopBtn.addEventListener('click', scrollToTop);

    // 键盘支持
    backToTopBtn.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        scrollToTop();
      }
    });

    // 初始检查
    handleScroll();
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBackToTop);
  } else {
    initBackToTop();
  }

  // 支持页面导航后重新初始化（SPA 场景）
  document.addEventListener('astro:page-load', initBackToTop);
</script>
