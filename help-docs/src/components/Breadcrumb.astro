---
// 面包屑导航组件
// 根据当前页面路径自动生成面包屑导航

interface BreadcrumbItem {
  label: string;
  href?: string;
}

// 获取当前页面路径
const currentPath = Astro.url.pathname;

// 定义路径映射
const pathMapping: Record<string, string> = {
  '/': '首页',
  '/overview/': '概要',
  '/overview/long-image/': '长图介绍',
  '/overview/demo/': '系统演示',
  '/system/': '系统设置',
  '/system/organizational/': '组织管理',
  '/system/organizational/department/': '部门管理',
  '/system/organizational/role/': '角色管理',
  '/system/organizational/employee/': '员工管理',
  '/system/organizational/login-log/': '登录日志',
  '/goods/': '商品',
  '/goods/manage/': '商品管理',
  '/goods/manage/publish-goods/': '商品列表',
  '/goods/manage/base-data-list/': '商品资料',
  '/goods/manage/goods-classify/': '商品分类',
  '/order/': '订单管理',
  '/order/manage/': '订单管理',
  '/order/manage/order-list/': '订货单',
  '/customer/': '客户管理',
  '/customer/manage/': '客户管理',
  '/customer/manage/customer-list/': '客户列表',
  '/customer/manage/customer-type/': '客户类型',
  '/purchase/': '采购管理',
  '/stock/': '库存系统',
  '/stock/section2/': '库存管理',
  '/stock/section2/query/': '库存查询',
  '/finance/': '财务',
  '/finance/section1/': '应收管理',
  '/finance/section1/customer-summary/': '客户往来汇总表',
  '/reports/': '报表',
  '/marketing/': '营销系统',
  '/other/': '其他相关内容',
};

// 生成面包屑路径
function generateBreadcrumbs(path: string): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [];
  
  // 添加首页
  breadcrumbs.push({ label: '首页', href: '/' });
  
  // 如果是首页，直接返回
  if (path === '/') {
    return [{ label: '首页' }];
  }
  
  // 分割路径并逐级构建面包屑
  const pathParts = path.split('/').filter(part => part !== '');
  let currentBuildPath = '';
  
  for (let i = 0; i < pathParts.length; i++) {
    currentBuildPath += '/' + pathParts[i] + '/';
    const label = pathMapping[currentBuildPath];
    
    if (label) {
      // 如果是最后一个路径，不添加链接
      if (i === pathParts.length - 1) {
        breadcrumbs.push({ label });
      } else {
        breadcrumbs.push({ label, href: currentBuildPath });
      }
    }
  }
  
  return breadcrumbs;
}

const breadcrumbs = generateBreadcrumbs(currentPath);
---

{breadcrumbs.length > 1 && (
  <nav class="breadcrumb-nav" aria-label="面包屑导航">
    <ol class="breadcrumb-list">
      {breadcrumbs.map((item, index) => (
        <li class="breadcrumb-item">
          {item.href ? (
            <a href={item.href} class="breadcrumb-link">
              {item.label}
            </a>
          ) : (
            <span class="breadcrumb-current" aria-current="page">
              {item.label}
            </span>
          )}
          {index < breadcrumbs.length - 1 && (
            <span class="breadcrumb-separator" aria-hidden="true">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z"/>
              </svg>
            </span>
          )}
        </li>
      ))}
    </ol>
  </nav>
)}

<style>
  .breadcrumb-nav {
    margin-bottom: 1.5rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--sl-color-gray-6);
  }

  .breadcrumb-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 0.875rem;
  }

  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .breadcrumb-link {
    color: var(--sl-color-text-accent);
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
  }

  .breadcrumb-link:hover {
    background: var(--sl-color-gray-6);
    text-decoration: underline;
  }

  .breadcrumb-current {
    color: var(--sl-color-text);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
  }

  .breadcrumb-separator {
    color: var(--sl-color-gray-3);
    display: flex;
    align-items: center;
    margin: 0 0.125rem;
  }

  .breadcrumb-separator svg {
    width: 12px;
    height: 12px;
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .breadcrumb-nav {
      margin-bottom: 1rem;
      padding: 0.5rem 0;
    }

    .breadcrumb-list {
      font-size: 0.8rem;
    }

    .breadcrumb-link,
    .breadcrumb-current {
      padding: 0.125rem 0.375rem;
    }

    /* 在移动端隐藏中间的面包屑，只显示首页和当前页 */
    .breadcrumb-item:not(:first-child):not(:last-child) {
      display: none;
    }

    /* 在隐藏中间项时，添加省略号 */
    .breadcrumb-item:first-child:not(:last-child)::after {
      content: "...";
      color: var(--sl-color-gray-3);
      margin: 0 0.5rem;
    }
  }

  /* 深色主题适配 */
  @media (prefers-color-scheme: dark) {
    .breadcrumb-nav {
      border-bottom-color: var(--sl-color-gray-5);
    }
  }
</style>
