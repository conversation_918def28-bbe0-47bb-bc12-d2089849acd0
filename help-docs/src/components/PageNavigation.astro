---
// 页面导航增强组件
// 提供更好的上一页/下一页导航体验

interface NavItem {
  title: string;
  href: string;
  description?: string;
}

interface Props {
  prev?: NavItem;
  next?: NavItem;
}

const { prev, next } = Astro.props;
---

{(prev || next) && (
  <nav class="page-navigation" aria-label="页面导航">
    <div class="nav-container">
      {prev && (
        <a href={prev.href} class="nav-link nav-prev">
          <div class="nav-direction">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
            <span>上一页</span>
          </div>
          <div class="nav-content">
            <div class="nav-title">{prev.title}</div>
            {prev.description && (
              <div class="nav-description">{prev.description}</div>
            )}
          </div>
        </a>
      )}
      
      {next && (
        <a href={next.href} class="nav-link nav-next">
          <div class="nav-content">
            <div class="nav-title">{next.title}</div>
            {next.description && (
              <div class="nav-description">{next.description}</div>
            )}
          </div>
          <div class="nav-direction">
            <span>下一页</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </div>
        </a>
      )}
    </div>
  </nav>
)}

<style>
  .page-navigation {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--sl-color-gray-6);
  }

  .nav-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    max-width: 100%;
  }

  .nav-link {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: var(--sl-color-gray-7);
    border: 1px solid var(--sl-color-gray-6);
    border-radius: 0.75rem;
    text-decoration: none;
    color: var(--sl-color-text);
    transition: all 0.2s ease;
    min-height: 5rem;
  }

  .nav-link:hover {
    background: var(--sl-color-gray-6);
    border-color: var(--sl-color-accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .nav-prev {
    justify-content: flex-start;
    text-align: left;
  }

  .nav-next {
    justify-content: flex-end;
    text-align: right;
    grid-column: 2;
  }

  /* 如果只有一个导航项，让它占满整行 */
  .nav-container:has(.nav-prev:only-child) .nav-prev,
  .nav-container:has(.nav-next:only-child) .nav-next {
    grid-column: 1 / -1;
  }

  .nav-direction {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--sl-color-text-accent);
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .nav-content {
    flex: 1;
    min-width: 0; /* 防止文本溢出 */
  }

  .nav-prev .nav-content {
    margin-left: 1rem;
  }

  .nav-next .nav-content {
    margin-right: 1rem;
  }

  .nav-title {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .nav-description {
    font-size: 0.875rem;
    color: var(--sl-color-gray-2);
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .page-navigation {
      margin-top: 2rem;
      padding-top: 1.5rem;
    }

    .nav-container {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .nav-next {
      grid-column: 1;
    }

    .nav-link {
      padding: 1rem;
      min-height: 4rem;
    }

    .nav-prev .nav-content {
      margin-left: 0.75rem;
    }

    .nav-next .nav-content {
      margin-right: 0.75rem;
    }

    .nav-title {
      font-size: 0.9rem;
    }

    .nav-description {
      font-size: 0.8rem;
    }
  }

  /* 减少动画效果（用户偏好） */
  @media (prefers-reduced-motion: reduce) {
    .nav-link {
      transition: background-color 0.2s ease, border-color 0.2s ease;
    }

    .nav-link:hover {
      transform: none;
      box-shadow: none;
    }
  }

  /* 深色主题适配 */
  @media (prefers-color-scheme: dark) {
    .page-navigation {
      border-top-color: var(--sl-color-gray-5);
    }

    .nav-link {
      background: var(--sl-color-gray-6);
      border-color: var(--sl-color-gray-5);
    }

    .nav-link:hover {
      background: var(--sl-color-gray-5);
    }
  }

  /* 高对比度模式适配 */
  @media (prefers-contrast: high) {
    .nav-link {
      border-width: 2px;
    }

    .nav-link:hover {
      border-color: var(--sl-color-accent-high);
    }
  }
</style>
