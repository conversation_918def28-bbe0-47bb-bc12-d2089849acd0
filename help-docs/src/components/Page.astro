---
// 自定义页面组件，集成面包屑导航和返回顶部功能
import Default from '@astrojs/starlight/components/Page.astro';
import Breadcrumb from './Breadcrumb.astro';
import BackToTop from './BackToTop.astro';
---

<!-- 面包屑导航 -->
<Breadcrumb />

<!-- 默认页面内容 -->
<Default><slot /></Default>

<!-- 返回顶部按钮 -->
<BackToTop />

<style>
  /* 为面包屑导航添加一些间距调整 */
  :global(.sl-container) {
    position: relative;
  }

  /* 确保返回顶部按钮不会被其他元素遮挡 */
  :global(#back-to-top) {
    z-index: 1000;
  }

  /* 优化页面内容的间距 */
  :global(.sl-markdown-content) {
    position: relative;
  }

  /* 为长页面添加阅读进度指示器 */
  :global(.reading-progress) {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--sl-color-accent);
    z-index: 1001;
    transition: width 0.1s ease;
  }
</style>

<script>
  // 添加阅读进度指示器
  function initReadingProgress() {
    // 创建进度条元素
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    document.body.appendChild(progressBar);

    // 计算阅读进度
    function updateProgress() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const progress = (scrollTop / scrollHeight) * 100;
      
      progressBar.style.width = Math.min(progress, 100) + '%';
    }

    // 节流函数
    function throttle(func: Function, limit: number) {
      let inThrottle: boolean;
      return function(this: any) {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    }

    // 监听滚动事件
    window.addEventListener('scroll', throttle(updateProgress, 50));
    
    // 初始更新
    updateProgress();
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initReadingProgress);
  } else {
    initReadingProgress();
  }

  // 支持页面导航后重新初始化
  document.addEventListener('astro:page-load', initReadingProgress);
</script>
